<?php
require_once '../config/database.php';
require_once '../classes/User.php';

$page_title = 'लॉगिन';
$meta_description = 'अपने खाते में लॉगिन करें - वेब डेवलपमेंट सर्विसेज';

// Redirect if already logged in
if (User::isLoggedIn()) {
    $redirect_url = $_SESSION['role'] === 'admin' ? '../admin/dashboard.php' : '../client/dashboard.php';
    header("Location: $redirect_url");
    exit();
}

$error_message = '';
$success_message = '';

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    // Verify CSRF token
    if (!isset($_POST['csrf_token']) || !verifyCsrfToken($_POST['csrf_token'])) {
        $error_message = 'सुरक्षा त्रुटि। कृपया पुनः प्रयास करें।';
    } else {
        $email = sanitizeInput($_POST['email']);
        $password = $_POST['password'];
        $remember_me = isset($_POST['remember_me']);
        
        if (empty($email) || empty($password)) {
            $error_message = 'कृपया सभी फील्ड भरें।';
        } else {
            $user = new User();
            
            if ($user->login($email, $password)) {
                // Set remember me cookie if checked
                if ($remember_me) {
                    $cookie_value = base64_encode($email . ':' . time());
                    setcookie('remember_login', $cookie_value, time() + (30 * 24 * 60 * 60), '/'); // 30 days
                }
                
                $_SESSION['success_message'] = 'सफलतापूर्वक लॉगिन हो गए!';
                
                // Redirect based on role
                $redirect_url = $_SESSION['role'] === 'admin' ? '../admin/dashboard.php' : '../client/dashboard.php';
                
                // Check if there's a redirect URL in session
                if (isset($_SESSION['redirect_after_login'])) {
                    $redirect_url = $_SESSION['redirect_after_login'];
                    unset($_SESSION['redirect_after_login']);
                }
                
                header("Location: $redirect_url");
                exit();
            } else {
                $error_message = 'गलत ईमेल या पासवर्ड।';
            }
        }
    }
}

// Check for remember me cookie
$remembered_email = '';
if (isset($_COOKIE['remember_login'])) {
    $cookie_data = base64_decode($_COOKIE['remember_login']);
    $parts = explode(':', $cookie_data);
    if (count($parts) == 2) {
        $remembered_email = $parts[0];
    }
}

include '../includes/header.php';
?>

<div class="container mt-5 pt-5">
    <div class="row justify-content-center">
        <div class="col-md-6 col-lg-5">
            <div class="card shadow-lg border-0">
                <div class="card-header bg-primary text-white text-center py-4">
                    <h3 class="mb-0">
                        <i class="fas fa-sign-in-alt me-2"></i>
                        लॉगिन करें
                    </h3>
                </div>
                <div class="card-body p-4">
                    <?php if ($error_message): ?>
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-circle me-2"></i>
                            <?php echo $error_message; ?>
                        </div>
                    <?php endif; ?>
                    
                    <?php if ($success_message): ?>
                        <div class="alert alert-success">
                            <i class="fas fa-check-circle me-2"></i>
                            <?php echo $success_message; ?>
                        </div>
                    <?php endif; ?>
                    
                    <form method="POST" action="" id="loginForm">
                        <input type="hidden" name="csrf_token" value="<?php echo $_SESSION['csrf_token']; ?>">
                        
                        <div class="mb-3">
                            <label for="email" class="form-label">
                                <i class="fas fa-envelope me-1"></i>
                                ईमेल पता
                            </label>
                            <input type="email" class="form-control" id="email" name="email" 
                                   value="<?php echo htmlspecialchars($remembered_email); ?>" 
                                   placeholder="आपका ईमेल पता" required>
                        </div>
                        
                        <div class="mb-3">
                            <label for="password" class="form-label">
                                <i class="fas fa-lock me-1"></i>
                                पासवर्ड
                            </label>
                            <div class="input-group">
                                <input type="password" class="form-control" id="password" name="password" 
                                       placeholder="आपका पासवर्ड" required>
                                <button class="btn btn-outline-secondary" type="button" id="togglePassword">
                                    <i class="fas fa-eye"></i>
                                </button>
                            </div>
                        </div>
                        
                        <div class="mb-3 form-check">
                            <input type="checkbox" class="form-check-input" id="remember_me" name="remember_me" 
                                   <?php echo $remembered_email ? 'checked' : ''; ?>>
                            <label class="form-check-label" for="remember_me">
                                मुझे याद रखें
                            </label>
                        </div>
                        
                        <?php if (Config::RECAPTCHA_SITE_KEY): ?>
                        <div class="mb-3">
                            <div class="g-recaptcha" data-sitekey="<?php echo Config::RECAPTCHA_SITE_KEY; ?>"></div>
                        </div>
                        <?php endif; ?>
                        
                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary btn-lg">
                                <i class="fas fa-sign-in-alt me-2"></i>
                                लॉगिन करें
                            </button>
                        </div>
                    </form>
                    
                    <hr class="my-4">
                    
                    <div class="text-center">
                        <p class="mb-2">
                            <a href="forgot-password.php" class="text-decoration-none">
                                <i class="fas fa-key me-1"></i>
                                पासवर्ड भूल गए?
                            </a>
                        </p>
                        <p class="mb-0">
                            खाता नहीं है? 
                            <a href="register.php" class="text-decoration-none fw-bold">
                                <i class="fas fa-user-plus me-1"></i>
                                रजिस्टर करें
                            </a>
                        </p>
                    </div>
                </div>
            </div>
            
            <!-- Quick Access for Demo -->
            <div class="card mt-4 border-info">
                <div class="card-header bg-info text-white">
                    <h6 class="mb-0">
                        <i class="fas fa-info-circle me-2"></i>
                        डेमो एक्सेस
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-6">
                            <button class="btn btn-outline-primary btn-sm w-100" onclick="fillAdminCredentials()">
                                <i class="fas fa-user-shield me-1"></i>
                                एडमिन लॉगिन
                            </button>
                        </div>
                        <div class="col-6">
                            <button class="btn btn-outline-success btn-sm w-100" onclick="fillClientCredentials()">
                                <i class="fas fa-user me-1"></i>
                                क्लाइंट लॉगिन
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Toggle password visibility
document.getElementById('togglePassword').addEventListener('click', function() {
    const password = document.getElementById('password');
    const icon = this.querySelector('i');
    
    if (password.type === 'password') {
        password.type = 'text';
        icon.classList.remove('fa-eye');
        icon.classList.add('fa-eye-slash');
    } else {
        password.type = 'password';
        icon.classList.remove('fa-eye-slash');
        icon.classList.add('fa-eye');
    }
});

// Demo credentials
function fillAdminCredentials() {
    document.getElementById('email').value = '<EMAIL>';
    document.getElementById('password').value = 'admin123';
}

function fillClientCredentials() {
    document.getElementById('email').value = '<EMAIL>';
    document.getElementById('password').value = 'client123';
}

// Form validation
document.getElementById('loginForm').addEventListener('submit', function(e) {
    const email = document.getElementById('email').value;
    const password = document.getElementById('password').value;
    
    if (!email || !password) {
        e.preventDefault();
        alert('कृपया सभी फील्ड भरें।');
        return false;
    }
    
    if (!isValidEmail(email)) {
        e.preventDefault();
        alert('कृपया वैध ईमेल पता दर्ज करें।');
        return false;
    }
});

function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

// Auto-focus on first empty field
window.addEventListener('load', function() {
    const email = document.getElementById('email');
    const password = document.getElementById('password');
    
    if (!email.value) {
        email.focus();
    } else {
        password.focus();
    }
});
</script>

<?php include '../includes/footer.php'; ?>
