<?php
require_once 'config/database.php';

class User {
    private $conn;
    private $table_name = "users";
    
    public $id;
    public $username;
    public $email;
    public $password;
    public $full_name;
    public $phone;
    public $role;
    public $status;
    public $created_at;
    public $updated_at;
    
    public function __construct() {
        $this->conn = Config::getDB();
    }
    
    // Register new user
    public function register() {
        $query = "INSERT INTO " . $this->table_name . " 
                 (username, email, password, full_name, phone, role) 
                 VALUES (?, ?, ?, ?, ?, ?)";
        
        $stmt = $this->conn->prepare($query);
        
        // Hash password
        $hashed_password = password_hash($this->password, PASSWORD_DEFAULT);
        
        $stmt->bindParam(1, $this->username);
        $stmt->bindParam(2, $this->email);
        $stmt->bindParam(3, $hashed_password);
        $stmt->bindParam(4, $this->full_name);
        $stmt->bindParam(5, $this->phone);
        $stmt->bindParam(6, $this->role);
        
        if ($stmt->execute()) {
            $this->id = $this->conn->lastInsertId();
            return true;
        }
        return false;
    }
    
    // Login user
    public function login($email, $password) {
        $query = "SELECT id, username, email, password, full_name, phone, role, status 
                 FROM " . $this->table_name . " 
                 WHERE email = ? AND status = 'active'";
        
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(1, $email);
        $stmt->execute();
        
        if ($stmt->rowCount() > 0) {
            $row = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if (password_verify($password, $row['password'])) {
                $this->id = $row['id'];
                $this->username = $row['username'];
                $this->email = $row['email'];
                $this->full_name = $row['full_name'];
                $this->phone = $row['phone'];
                $this->role = $row['role'];
                $this->status = $row['status'];
                
                // Set session variables
                $_SESSION['user_id'] = $this->id;
                $_SESSION['username'] = $this->username;
                $_SESSION['email'] = $this->email;
                $_SESSION['full_name'] = $this->full_name;
                $_SESSION['role'] = $this->role;
                $_SESSION['login_time'] = time();
                
                // Log login activity
                $this->logActivity('login', 'User logged in successfully');
                
                return true;
            }
        }
        return false;
    }
    
    // Check if user exists
    public function userExists($email, $username = null) {
        $query = "SELECT id FROM " . $this->table_name . " WHERE email = ?";
        $params = [$email];
        
        if ($username) {
            $query .= " OR username = ?";
            $params[] = $username;
        }
        
        $stmt = $this->conn->prepare($query);
        $stmt->execute($params);
        
        return $stmt->rowCount() > 0;
    }
    
    // Get user by ID
    public function getUserById($id) {
        $query = "SELECT * FROM " . $this->table_name . " WHERE id = ?";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(1, $id);
        $stmt->execute();
        
        if ($stmt->rowCount() > 0) {
            $row = $stmt->fetch(PDO::FETCH_ASSOC);
            $this->id = $row['id'];
            $this->username = $row['username'];
            $this->email = $row['email'];
            $this->full_name = $row['full_name'];
            $this->phone = $row['phone'];
            $this->role = $row['role'];
            $this->status = $row['status'];
            $this->created_at = $row['created_at'];
            $this->updated_at = $row['updated_at'];
            return true;
        }
        return false;
    }
    
    // Update user profile
    public function updateProfile() {
        $query = "UPDATE " . $this->table_name . " 
                 SET full_name = ?, phone = ?, updated_at = CURRENT_TIMESTAMP 
                 WHERE id = ?";
        
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(1, $this->full_name);
        $stmt->bindParam(2, $this->phone);
        $stmt->bindParam(3, $this->id);
        
        if ($stmt->execute()) {
            $_SESSION['full_name'] = $this->full_name;
            $this->logActivity('profile_update', 'Profile updated successfully');
            return true;
        }
        return false;
    }
    
    // Change password
    public function changePassword($current_password, $new_password) {
        // First verify current password
        $query = "SELECT password FROM " . $this->table_name . " WHERE id = ?";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(1, $this->id);
        $stmt->execute();
        
        if ($stmt->rowCount() > 0) {
            $row = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if (password_verify($current_password, $row['password'])) {
                // Update password
                $query = "UPDATE " . $this->table_name . " 
                         SET password = ?, updated_at = CURRENT_TIMESTAMP 
                         WHERE id = ?";
                
                $stmt = $this->conn->prepare($query);
                $hashed_password = password_hash($new_password, PASSWORD_DEFAULT);
                $stmt->bindParam(1, $hashed_password);
                $stmt->bindParam(2, $this->id);
                
                if ($stmt->execute()) {
                    $this->logActivity('password_change', 'Password changed successfully');
                    return true;
                }
            }
        }
        return false;
    }
    
    // Get all users (admin only)
    public function getAllUsers($limit = 20, $offset = 0) {
        $query = "SELECT id, username, email, full_name, phone, role, status, created_at 
                 FROM " . $this->table_name . " 
                 ORDER BY created_at DESC 
                 LIMIT ? OFFSET ?";
        
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(1, $limit, PDO::PARAM_INT);
        $stmt->bindParam(2, $offset, PDO::PARAM_INT);
        $stmt->execute();
        
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    // Update user status (admin only)
    public function updateStatus($user_id, $status) {
        $query = "UPDATE " . $this->table_name . " 
                 SET status = ?, updated_at = CURRENT_TIMESTAMP 
                 WHERE id = ?";
        
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(1, $status);
        $stmt->bindParam(2, $user_id);
        
        if ($stmt->execute()) {
            $this->logActivity('user_status_update', "User ID $user_id status changed to $status");
            return true;
        }
        return false;
    }
    
    // Check if user is admin
    public function isAdmin() {
        return isset($_SESSION['role']) && $_SESSION['role'] === 'admin';
    }
    
    // Check if user is logged in
    public static function isLoggedIn() {
        return isset($_SESSION['user_id']) && !empty($_SESSION['user_id']);
    }
    
    // Logout user
    public static function logout() {
        if (self::isLoggedIn()) {
            $user = new User();
            $user->id = $_SESSION['user_id'];
            $user->logActivity('logout', 'User logged out');
        }
        
        session_unset();
        session_destroy();
        return true;
    }
    
    // Log user activity
    private function logActivity($action, $details = '') {
        try {
            $query = "INSERT INTO activity_logs (user_id, action, details, ip_address, created_at) 
                     VALUES (?, ?, ?, ?, NOW())";
            $stmt = $this->conn->prepare($query);
            $ip_address = getUserIP();
            $stmt->execute([$this->id, $action, $details, $ip_address]);
        } catch(Exception $e) {
            // Log error but don't break the flow
            error_log("Activity logging failed: " . $e->getMessage());
        }
    }
    
    // Validate input data
    public function validateRegistration($data) {
        $errors = [];
        
        if (empty($data['username']) || strlen($data['username']) < 3) {
            $errors[] = 'उपयोगकर्ता नाम कम से कम 3 अक्षर का होना चाहिए';
        }
        
        if (empty($data['email']) || !filter_var($data['email'], FILTER_VALIDATE_EMAIL)) {
            $errors[] = 'वैध ईमेल पता दर्ज करें';
        }
        
        if (empty($data['password']) || strlen($data['password']) < Config::PASSWORD_MIN_LENGTH) {
            $errors[] = 'पासवर्ड कम से कम ' . Config::PASSWORD_MIN_LENGTH . ' अक्षर का होना चाहिए';
        }
        
        if ($data['password'] !== $data['confirm_password']) {
            $errors[] = 'पासवर्ड और पुष्टि पासवर्ड मेल नहीं खाते';
        }
        
        if (empty($data['full_name']) || strlen($data['full_name']) < 2) {
            $errors[] = 'पूरा नाम दर्ज करें';
        }
        
        if (!empty($data['phone']) && !isValidPhone($data['phone'])) {
            $errors[] = 'वैध फोन नंबर दर्ज करें';
        }
        
        // Check if user already exists
        if ($this->userExists($data['email'], $data['username'])) {
            $errors[] = 'यह ईमेल या उपयोगकर्ता नाम पहले से मौजूद है';
        }
        
        return $errors;
    }
    
    // Get user statistics (admin dashboard)
    public function getUserStats() {
        $stats = [];
        
        // Total users
        $query = "SELECT COUNT(*) as total FROM " . $this->table_name;
        $stmt = $this->conn->prepare($query);
        $stmt->execute();
        $stats['total_users'] = $stmt->fetch()['total'];
        
        // Active users
        $query = "SELECT COUNT(*) as active FROM " . $this->table_name . " WHERE status = 'active'";
        $stmt = $this->conn->prepare($query);
        $stmt->execute();
        $stats['active_users'] = $stmt->fetch()['active'];
        
        // New users this month
        $query = "SELECT COUNT(*) as new_this_month FROM " . $this->table_name . " 
                 WHERE MONTH(created_at) = MONTH(CURRENT_DATE()) 
                 AND YEAR(created_at) = YEAR(CURRENT_DATE())";
        $stmt = $this->conn->prepare($query);
        $stmt->execute();
        $stats['new_this_month'] = $stmt->fetch()['new_this_month'];
        
        return $stats;
    }
}
?>
