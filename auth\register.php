<?php
require_once '../config/database.php';
require_once '../classes/User.php';

$page_title = 'रजिस्टर करें';
$meta_description = 'नया खाता बनाएं - वेब डेवलपमेंट सर्विसेज';

// Redirect if already logged in
if (User::isLoggedIn()) {
    $redirect_url = $_SESSION['role'] === 'admin' ? '../admin/dashboard.php' : '../client/dashboard.php';
    header("Location: $redirect_url");
    exit();
}

$error_messages = [];
$success_message = '';
$form_data = [];

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    // Verify CSRF token
    if (!isset($_POST['csrf_token']) || !verifyCsrfToken($_POST['csrf_token'])) {
        $error_messages[] = 'सुरक्षा त्रुटि। कृपया पुनः प्रयास करें।';
    } else {
        // Sanitize input data
        $form_data = [
            'username' => sanitizeInput($_POST['username']),
            'email' => sanitizeInput($_POST['email']),
            'password' => $_POST['password'],
            'confirm_password' => $_POST['confirm_password'],
            'full_name' => sanitizeInput($_POST['full_name']),
            'phone' => sanitizeInput($_POST['phone'])
        ];
        
        // Verify reCAPTCHA if enabled
        if (Config::RECAPTCHA_SECRET_KEY && isset($_POST['g-recaptcha-response'])) {
            $recaptcha_response = $_POST['g-recaptcha-response'];
            $recaptcha_url = 'https://www.google.com/recaptcha/api/siteverify';
            $recaptcha_data = [
                'secret' => Config::RECAPTCHA_SECRET_KEY,
                'response' => $recaptcha_response,
                'remoteip' => getUserIP()
            ];
            
            $recaptcha_options = [
                'http' => [
                    'header' => "Content-type: application/x-www-form-urlencoded\r\n",
                    'method' => 'POST',
                    'content' => http_build_query($recaptcha_data)
                ]
            ];
            
            $recaptcha_context = stream_context_create($recaptcha_options);
            $recaptcha_result = file_get_contents($recaptcha_url, false, $recaptcha_context);
            $recaptcha_json = json_decode($recaptcha_result, true);
            
            if (!$recaptcha_json['success']) {
                $error_messages[] = 'reCAPTCHA सत्यापन असफल। कृपया पुनः प्रयास करें।';
            }
        }
        
        if (empty($error_messages)) {
            $user = new User();
            $validation_errors = $user->validateRegistration($form_data);
            
            if (empty($validation_errors)) {
                // Set user properties
                $user->username = $form_data['username'];
                $user->email = $form_data['email'];
                $user->password = $form_data['password'];
                $user->full_name = $form_data['full_name'];
                $user->phone = $form_data['phone'];
                $user->role = 'client'; // Default role
                
                if ($user->register()) {
                    $_SESSION['success_message'] = 'खाता सफलतापूर्वक बनाया गया! कृपया लॉगिन करें।';
                    header('Location: login.php');
                    exit();
                } else {
                    $error_messages[] = 'रजिस्ट्रेशन में त्रुटि। कृपया बाद में पुनः प्रयास करें।';
                }
            } else {
                $error_messages = $validation_errors;
            }
        }
    }
}

include '../includes/header.php';
?>

<div class="container mt-5 pt-5">
    <div class="row justify-content-center">
        <div class="col-md-8 col-lg-6">
            <div class="card shadow-lg border-0">
                <div class="card-header bg-success text-white text-center py-4">
                    <h3 class="mb-0">
                        <i class="fas fa-user-plus me-2"></i>
                        नया खाता बनाएं
                    </h3>
                </div>
                <div class="card-body p-4">
                    <?php if (!empty($error_messages)): ?>
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-circle me-2"></i>
                            <ul class="mb-0">
                                <?php foreach ($error_messages as $error): ?>
                                    <li><?php echo $error; ?></li>
                                <?php endforeach; ?>
                            </ul>
                        </div>
                    <?php endif; ?>
                    
                    <form method="POST" action="" id="registerForm">
                        <input type="hidden" name="csrf_token" value="<?php echo $_SESSION['csrf_token']; ?>">
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="username" class="form-label">
                                    <i class="fas fa-user me-1"></i>
                                    उपयोगकर्ता नाम *
                                </label>
                                <input type="text" class="form-control" id="username" name="username" 
                                       value="<?php echo htmlspecialchars($form_data['username'] ?? ''); ?>" 
                                       placeholder="उपयोगकर्ता नाम" required>
                                <div class="form-text">कम से कम 3 अक्षर</div>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="email" class="form-label">
                                    <i class="fas fa-envelope me-1"></i>
                                    ईमेल पता *
                                </label>
                                <input type="email" class="form-control" id="email" name="email" 
                                       value="<?php echo htmlspecialchars($form_data['email'] ?? ''); ?>" 
                                       placeholder="आपका ईमेल पता" required>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="full_name" class="form-label">
                                <i class="fas fa-id-card me-1"></i>
                                पूरा नाम *
                            </label>
                            <input type="text" class="form-control" id="full_name" name="full_name" 
                                   value="<?php echo htmlspecialchars($form_data['full_name'] ?? ''); ?>" 
                                   placeholder="आपका पूरा नाम" required>
                        </div>
                        
                        <div class="mb-3">
                            <label for="phone" class="form-label">
                                <i class="fas fa-phone me-1"></i>
                                फोन नंबर
                            </label>
                            <input type="tel" class="form-control" id="phone" name="phone" 
                                   value="<?php echo htmlspecialchars($form_data['phone'] ?? ''); ?>" 
                                   placeholder="+91-9876543210">
                            <div class="form-text">वैकल्पिक - भारतीय फोन नंबर</div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="password" class="form-label">
                                    <i class="fas fa-lock me-1"></i>
                                    पासवर्ड *
                                </label>
                                <div class="input-group">
                                    <input type="password" class="form-control" id="password" name="password" 
                                           placeholder="पासवर्ड" required>
                                    <button class="btn btn-outline-secondary" type="button" id="togglePassword">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                </div>
                                <div class="form-text">कम से कम <?php echo Config::PASSWORD_MIN_LENGTH; ?> अक्षर</div>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="confirm_password" class="form-label">
                                    <i class="fas fa-lock me-1"></i>
                                    पासवर्ड पुष्टि *
                                </label>
                                <div class="input-group">
                                    <input type="password" class="form-control" id="confirm_password" name="confirm_password" 
                                           placeholder="पासवर्ड दोबारा दर्ज करें" required>
                                    <button class="btn btn-outline-secondary" type="button" id="toggleConfirmPassword">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                        
                        <div class="mb-3 form-check">
                            <input type="checkbox" class="form-check-input" id="terms" name="terms" required>
                            <label class="form-check-label" for="terms">
                                मैं <a href="../legal/terms-of-service.php" target="_blank">सेवा की शर्तों</a> 
                                और <a href="../legal/privacy-policy.php" target="_blank">गोपनीयता नीति</a> से सहमत हूं *
                            </label>
                        </div>
                        
                        <?php if (Config::RECAPTCHA_SITE_KEY): ?>
                        <div class="mb-3">
                            <div class="g-recaptcha" data-sitekey="<?php echo Config::RECAPTCHA_SITE_KEY; ?>"></div>
                        </div>
                        <?php endif; ?>
                        
                        <div class="d-grid">
                            <button type="submit" class="btn btn-success btn-lg">
                                <i class="fas fa-user-plus me-2"></i>
                                खाता बनाएं
                            </button>
                        </div>
                    </form>
                    
                    <hr class="my-4">
                    
                    <div class="text-center">
                        <p class="mb-0">
                            पहले से खाता है? 
                            <a href="login.php" class="text-decoration-none fw-bold">
                                <i class="fas fa-sign-in-alt me-1"></i>
                                लॉगिन करें
                            </a>
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Toggle password visibility
function togglePasswordField(fieldId, buttonId) {
    const field = document.getElementById(fieldId);
    const button = document.getElementById(buttonId);
    const icon = button.querySelector('i');
    
    if (field.type === 'password') {
        field.type = 'text';
        icon.classList.remove('fa-eye');
        icon.classList.add('fa-eye-slash');
    } else {
        field.type = 'password';
        icon.classList.remove('fa-eye-slash');
        icon.classList.add('fa-eye');
    }
}

document.getElementById('togglePassword').addEventListener('click', function() {
    togglePasswordField('password', 'togglePassword');
});

document.getElementById('toggleConfirmPassword').addEventListener('click', function() {
    togglePasswordField('confirm_password', 'toggleConfirmPassword');
});

// Form validation
document.getElementById('registerForm').addEventListener('submit', function(e) {
    const username = document.getElementById('username').value;
    const email = document.getElementById('email').value;
    const fullName = document.getElementById('full_name').value;
    const password = document.getElementById('password').value;
    const confirmPassword = document.getElementById('confirm_password').value;
    const phone = document.getElementById('phone').value;
    const terms = document.getElementById('terms').checked;
    
    let errors = [];
    
    if (username.length < 3) {
        errors.push('उपयोगकर्ता नाम कम से कम 3 अक्षर का होना चाहिए');
    }
    
    if (!isValidEmail(email)) {
        errors.push('वैध ईमेल पता दर्ज करें');
    }
    
    if (fullName.length < 2) {
        errors.push('पूरा नाम दर्ज करें');
    }
    
    if (password.length < <?php echo Config::PASSWORD_MIN_LENGTH; ?>) {
        errors.push('पासवर्ड कम से कम <?php echo Config::PASSWORD_MIN_LENGTH; ?> अक्षर का होना चाहिए');
    }
    
    if (password !== confirmPassword) {
        errors.push('पासवर्ड और पुष्टि पासवर्ड मेल नहीं खाते');
    }
    
    if (phone && !isValidPhone(phone)) {
        errors.push('वैध फोन नंबर दर्ज करें');
    }
    
    if (!terms) {
        errors.push('कृपया सेवा की शर्तों से सहमति दें');
    }
    
    if (errors.length > 0) {
        e.preventDefault();
        alert(errors.join('\n'));
        return false;
    }
});

function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

function isValidPhone(phone) {
    const phoneRegex = /^(\+91[\-\s]?)?[0]?(91)?[789]\d{9}$/;
    return phoneRegex.test(phone);
}

// Real-time password strength indicator
document.getElementById('password').addEventListener('input', function() {
    const password = this.value;
    const strength = calculatePasswordStrength(password);
    updatePasswordStrengthIndicator(strength);
});

function calculatePasswordStrength(password) {
    let strength = 0;
    if (password.length >= 8) strength++;
    if (/[a-z]/.test(password)) strength++;
    if (/[A-Z]/.test(password)) strength++;
    if (/[0-9]/.test(password)) strength++;
    if (/[^A-Za-z0-9]/.test(password)) strength++;
    return strength;
}

function updatePasswordStrengthIndicator(strength) {
    // This can be enhanced with a visual indicator
    const strengthTexts = ['बहुत कमजोर', 'कमजोर', 'मध्यम', 'मजबूत', 'बहुत मजबूत'];
    const strengthColors = ['#dc3545', '#fd7e14', '#ffc107', '#28a745', '#20c997'];
    
    // Add strength indicator if needed
}
</script>

<?php include '../includes/footer.php'; ?>
