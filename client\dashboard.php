<?php
require_once '../config/database.php';
require_once '../classes/User.php';
require_once '../classes/Order.php';

// Check if user is logged in
if (!User::isLoggedIn()) {
    header('Location: ../auth/login.php');
    exit();
}

// Redirect admin to admin dashboard
if ($_SESSION['role'] === 'admin') {
    header('Location: ../admin/dashboard.php');
    exit();
}

$page_title = 'क्लाइंट डैशबोर्ड';
$meta_description = 'अपने ऑर्डर्स देखें और ट्रैक करें - वेब डेवलपमेंट सर्विसेज';

// Get user's orders
$order = new Order();
$user_orders = $order->getByUser($_SESSION['user_id'], 10, 0);

// Get order statistics for this user
try {
    $db = Config::getDB();
    
    // Total orders
    $stmt = $db->prepare("SELECT COUNT(*) as total FROM orders WHERE user_id = ?");
    $stmt->execute([$_SESSION['user_id']]);
    $total_orders = $stmt->fetch()['total'];
    
    // Pending orders
    $stmt = $db->prepare("SELECT COUNT(*) as pending FROM orders WHERE user_id = ? AND status = 'pending'");
    $stmt->execute([$_SESSION['user_id']]);
    $pending_orders = $stmt->fetch()['pending'];
    
    // Completed orders
    $stmt = $db->prepare("SELECT COUNT(*) as completed FROM orders WHERE user_id = ? AND status = 'completed'");
    $stmt->execute([$_SESSION['user_id']]);
    $completed_orders = $stmt->fetch()['completed'];
    
    // In progress orders
    $stmt = $db->prepare("SELECT COUNT(*) as in_progress FROM orders WHERE user_id = ? AND status = 'in_progress'");
    $stmt->execute([$_SESSION['user_id']]);
    $in_progress_orders = $stmt->fetch()['in_progress'];
    
} catch(Exception $e) {
    $total_orders = $pending_orders = $completed_orders = $in_progress_orders = 0;
}

include '../includes/header.php';
?>

<div class="container mt-5 pt-5">
    <!-- Welcome Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card bg-gradient-primary text-white border-0 shadow-lg">
                <div class="card-body p-4">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <h2 class="mb-2">
                                <i class="fas fa-user-circle me-2"></i>
                                नमस्ते, <?php echo htmlspecialchars($_SESSION['full_name']); ?>!
                            </h2>
                            <p class="mb-0 lead">आपके वेब डेवलपमेंट प्रोजेक्ट्स का डैशबोर्ड</p>
                        </div>
                        <div class="col-md-4 text-md-end">
                            <a href="../services.php" class="btn btn-warning btn-lg">
                                <i class="fas fa-plus me-2"></i>
                                नया ऑर्डर करें
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row g-4 mb-5">
        <div class="col-md-3">
            <div class="card border-0 shadow-sm">
                <div class="card-body text-center">
                    <div class="stat-icon bg-primary text-white rounded-circle mx-auto mb-3">
                        <i class="fas fa-shopping-cart fa-2x"></i>
                    </div>
                    <h3 class="fw-bold text-primary"><?php echo $total_orders; ?></h3>
                    <p class="text-muted mb-0">कुल ऑर्डर</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-0 shadow-sm">
                <div class="card-body text-center">
                    <div class="stat-icon bg-warning text-white rounded-circle mx-auto mb-3">
                        <i class="fas fa-clock fa-2x"></i>
                    </div>
                    <h3 class="fw-bold text-warning"><?php echo $pending_orders; ?></h3>
                    <p class="text-muted mb-0">पेंडिंग ऑर्डर</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-0 shadow-sm">
                <div class="card-body text-center">
                    <div class="stat-icon bg-info text-white rounded-circle mx-auto mb-3">
                        <i class="fas fa-cogs fa-2x"></i>
                    </div>
                    <h3 class="fw-bold text-info"><?php echo $in_progress_orders; ?></h3>
                    <p class="text-muted mb-0">प्रगति में</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-0 shadow-sm">
                <div class="card-body text-center">
                    <div class="stat-icon bg-success text-white rounded-circle mx-auto mb-3">
                        <i class="fas fa-check-circle fa-2x"></i>
                    </div>
                    <h3 class="fw-bold text-success"><?php echo $completed_orders; ?></h3>
                    <p class="text-muted mb-0">पूर्ण</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Orders -->
    <div class="row">
        <div class="col-12">
            <div class="card shadow-lg border-0">
                <div class="card-header bg-white py-4">
                    <div class="row align-items-center">
                        <div class="col-md-6">
                            <h4 class="mb-0">
                                <i class="fas fa-list me-2"></i>
                                हाल के ऑर्डर
                            </h4>
                        </div>
                        <div class="col-md-6 text-md-end">
                            <a href="orders.php" class="btn btn-outline-primary">
                                <i class="fas fa-eye me-2"></i>
                                सभी ऑर्डर देखें
                            </a>
                        </div>
                    </div>
                </div>
                <div class="card-body p-0">
                    <?php if (empty($user_orders)): ?>
                        <div class="text-center py-5">
                            <i class="fas fa-shopping-cart fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">अभी तक कोई ऑर्डर नहीं</h5>
                            <p class="text-muted">अपना पहला ऑर्डर करने के लिए नीचे दिए गए बटन पर क्लिक करें।</p>
                            <a href="../services.php" class="btn btn-primary">
                                <i class="fas fa-plus me-2"></i>
                                पहला ऑर्डर करें
                            </a>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-hover mb-0">
                                <thead class="bg-light">
                                    <tr>
                                        <th>ऑर्डर नंबर</th>
                                        <th>सेवा</th>
                                        <th>राशि</th>
                                        <th>स्थिति</th>
                                        <th>पेमेंट</th>
                                        <th>दिनांक</th>
                                        <th>कार्य</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($user_orders as $order_item): ?>
                                        <tr>
                                            <td>
                                                <strong class="text-primary"><?php echo htmlspecialchars($order_item['order_number']); ?></strong>
                                            </td>
                                            <td><?php echo htmlspecialchars($order_item['service_name_hindi']); ?></td>
                                            <td>
                                                <?php if ($order_item['total_amount'] > 0): ?>
                                                    <strong>₹<?php echo number_format($order_item['total_amount']); ?></strong>
                                                <?php else: ?>
                                                    <span class="text-muted">कस्टम</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <?php
                                                $status_classes = [
                                                    'pending' => 'warning',
                                                    'confirmed' => 'info',
                                                    'in_progress' => 'primary',
                                                    'completed' => 'success',
                                                    'cancelled' => 'danger'
                                                ];
                                                $status_labels = [
                                                    'pending' => 'पेंडिंग',
                                                    'confirmed' => 'पुष्ट',
                                                    'in_progress' => 'प्रगति में',
                                                    'completed' => 'पूर्ण',
                                                    'cancelled' => 'रद्द'
                                                ];
                                                $status_class = $status_classes[$order_item['status']] ?? 'secondary';
                                                $status_label = $status_labels[$order_item['status']] ?? $order_item['status'];
                                                ?>
                                                <span class="badge bg-<?php echo $status_class; ?>">
                                                    <?php echo $status_label; ?>
                                                </span>
                                            </td>
                                            <td>
                                                <?php
                                                $payment_classes = [
                                                    'pending' => 'warning',
                                                    'paid' => 'success',
                                                    'failed' => 'danger',
                                                    'refunded' => 'info'
                                                ];
                                                $payment_labels = [
                                                    'pending' => 'पेंडिंग',
                                                    'paid' => 'भुगतान',
                                                    'failed' => 'असफल',
                                                    'refunded' => 'वापसी'
                                                ];
                                                $payment_class = $payment_classes[$order_item['payment_status']] ?? 'secondary';
                                                $payment_label = $payment_labels[$order_item['payment_status']] ?? $order_item['payment_status'];
                                                ?>
                                                <span class="badge bg-<?php echo $payment_class; ?>">
                                                    <?php echo $payment_label; ?>
                                                </span>
                                            </td>
                                            <td>
                                                <small class="text-muted">
                                                    <?php echo date('d/m/Y', strtotime($order_item['created_at'])); ?>
                                                </small>
                                            </td>
                                            <td>
                                                <div class="btn-group btn-group-sm">
                                                    <a href="order-details.php?id=<?php echo $order_item['id']; ?>" 
                                                       class="btn btn-outline-primary btn-sm">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    <?php if ($order_item['payment_status'] === 'pending' && $order_item['total_amount'] > 0): ?>
                                                        <a href="../payment.php?order=<?php echo $order_item['order_number']; ?>" 
                                                           class="btn btn-outline-success btn-sm">
                                                            <i class="fas fa-credit-card"></i>
                                                        </a>
                                                    <?php endif; ?>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="row mt-5">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-light">
                    <h5 class="mb-0">
                        <i class="fas fa-bolt me-2"></i>
                        त्वरित कार्य
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row g-3">
                        <div class="col-md-3">
                            <a href="../services.php" class="btn btn-outline-primary w-100">
                                <i class="fas fa-plus-circle me-2"></i>
                                नया ऑर्डर
                            </a>
                        </div>
                        <div class="col-md-3">
                            <a href="orders.php" class="btn btn-outline-info w-100">
                                <i class="fas fa-list me-2"></i>
                                सभी ऑर्डर
                            </a>
                        </div>
                        <div class="col-md-3">
                            <a href="profile.php" class="btn btn-outline-success w-100">
                                <i class="fas fa-user-edit me-2"></i>
                                प्रोफाइल अपडेट
                            </a>
                        </div>
                        <div class="col-md-3">
                            <a href="../contact.php" class="btn btn-outline-warning w-100">
                                <i class="fas fa-headset me-2"></i>
                                सहायता
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.stat-icon {
    width: 80px;
    height: 80px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.table th {
    border-top: none;
    font-weight: 600;
    color: #495057;
}

.btn-group-sm .btn {
    padding: 0.25rem 0.5rem;
}

@media (max-width: 768px) {
    .table-responsive {
        font-size: 0.875rem;
    }
    
    .btn-group-sm .btn {
        padding: 0.125rem 0.25rem;
    }
}
</style>

<script>
// Auto-refresh order status every 30 seconds
setInterval(function() {
    // Only refresh if there are pending or in-progress orders
    const hasPendingOrders = <?php echo ($pending_orders + $in_progress_orders) > 0 ? 'true' : 'false'; ?>;
    if (hasPendingOrders) {
        // Subtle refresh without full page reload
        fetch(window.location.href)
            .then(response => response.text())
            .then(html => {
                // Update only the orders table
                const parser = new DOMParser();
                const newDoc = parser.parseFromString(html, 'text/html');
                const newTable = newDoc.querySelector('.table-responsive');
                const currentTable = document.querySelector('.table-responsive');
                
                if (newTable && currentTable) {
                    currentTable.innerHTML = newTable.innerHTML;
                }
            })
            .catch(error => console.log('Auto-refresh failed:', error));
    }
}, 30000);

// Add tooltips to action buttons
document.addEventListener('DOMContentLoaded', function() {
    const tooltips = {
        'fa-eye': 'विवरण देखें',
        'fa-credit-card': 'पेमेंट करें'
    };
    
    document.querySelectorAll('.btn-group-sm .btn').forEach(btn => {
        const icon = btn.querySelector('i');
        if (icon) {
            const iconClass = Array.from(icon.classList).find(cls => cls.startsWith('fa-'));
            if (tooltips[iconClass]) {
                btn.title = tooltips[iconClass];
            }
        }
    });
});
</script>

<?php include '../includes/footer.php'; ?>
