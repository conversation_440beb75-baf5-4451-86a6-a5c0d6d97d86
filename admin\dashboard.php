<?php
require_once '../config/database.php';
require_once '../classes/User.php';
require_once '../classes/Order.php';

// Check if user is logged in and is admin
if (!User::isLoggedIn() || $_SESSION['role'] !== 'admin') {
    header('Location: ../auth/login.php');
    exit();
}

$page_title = 'एडमिन डैशबोर्ड';
$meta_description = 'वेब डेवलपमेंट सर्विसेज एडमिन पैनल - ऑर्डर, क्लाइंट और सेवा प्रबंधन';

// Get dashboard statistics
$order = new Order();
$user = new User();

$order_stats = $order->getStats();
$user_stats = $user->getUserStats();

// Get recent orders
$recent_orders = $order->getAll(5, 0);

// Get recent users
$recent_users = $user->getAllUsers(5, 0);

// Monthly revenue data for chart
try {
    $db = Config::getDB();
    $stmt = $db->prepare("
        SELECT 
            MONTH(created_at) as month,
            YEAR(created_at) as year,
            SUM(total_amount) as revenue,
            COUNT(*) as order_count
        FROM orders 
        WHERE payment_status = 'paid' 
        AND created_at >= DATE_SUB(NOW(), INTERVAL 12 MONTH)
        GROUP BY YEAR(created_at), MONTH(created_at)
        ORDER BY year DESC, month DESC
        LIMIT 12
    ");
    $stmt->execute();
    $monthly_data = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch(Exception $e) {
    $monthly_data = [];
}

include '../includes/header.php';
?>

<div class="container-fluid mt-5 pt-5">
    <!-- Welcome Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card bg-gradient-primary text-white border-0 shadow-lg">
                <div class="card-body p-4">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <h2 class="mb-2">
                                <i class="fas fa-tachometer-alt me-2"></i>
                                एडमिन डैशबोर्ड
                            </h2>
                            <p class="mb-0 lead">वेब डेवलपमेंट सर्विसेज प्रबंधन पैनल</p>
                        </div>
                        <div class="col-md-4 text-md-end">
                            <div class="btn-group">
                                <a href="orders.php" class="btn btn-warning">
                                    <i class="fas fa-list me-2"></i>
                                    सभी ऑर्डर
                                </a>
                                <a href="users.php" class="btn btn-info">
                                    <i class="fas fa-users me-2"></i>
                                    सभी यूजर
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row g-4 mb-5">
        <div class="col-xl-3 col-md-6">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col">
                            <h6 class="text-uppercase text-muted mb-2">कुल ऑर्डर</h6>
                            <h2 class="mb-0 text-primary"><?php echo $order_stats['total_orders']; ?></h2>
                            <small class="text-muted">इस महीने: <?php echo $order_stats['orders_this_month']; ?></small>
                        </div>
                        <div class="col-auto">
                            <div class="stat-icon bg-primary text-white rounded-circle">
                                <i class="fas fa-shopping-cart fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-xl-3 col-md-6">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col">
                            <h6 class="text-uppercase text-muted mb-2">पेंडिंग ऑर्डर</h6>
                            <h2 class="mb-0 text-warning"><?php echo $order_stats['pending_orders']; ?></h2>
                            <small class="text-muted">तुरंत ध्यान दें</small>
                        </div>
                        <div class="col-auto">
                            <div class="stat-icon bg-warning text-white rounded-circle">
                                <i class="fas fa-clock fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-xl-3 col-md-6">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col">
                            <h6 class="text-uppercase text-muted mb-2">कुल रेवेन्यू</h6>
                            <h2 class="mb-0 text-success">₹<?php echo number_format($order_stats['total_revenue']); ?></h2>
                            <small class="text-muted">भुगतान प्राप्त</small>
                        </div>
                        <div class="col-auto">
                            <div class="stat-icon bg-success text-white rounded-circle">
                                <i class="fas fa-rupee-sign fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-xl-3 col-md-6">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col">
                            <h6 class="text-uppercase text-muted mb-2">कुल यूजर</h6>
                            <h2 class="mb-0 text-info"><?php echo $user_stats['total_users']; ?></h2>
                            <small class="text-muted">सक्रिय: <?php echo $user_stats['active_users']; ?></small>
                        </div>
                        <div class="col-auto">
                            <div class="stat-icon bg-info text-white rounded-circle">
                                <i class="fas fa-users fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Recent Orders -->
        <div class="col-lg-8 mb-4">
            <div class="card shadow-lg border-0">
                <div class="card-header bg-white py-3">
                    <div class="row align-items-center">
                        <div class="col">
                            <h5 class="mb-0">
                                <i class="fas fa-list me-2"></i>
                                हाल के ऑर्डर
                            </h5>
                        </div>
                        <div class="col-auto">
                            <a href="orders.php" class="btn btn-outline-primary btn-sm">
                                सभी देखें
                            </a>
                        </div>
                    </div>
                </div>
                <div class="card-body p-0">
                    <?php if (empty($recent_orders)): ?>
                        <div class="text-center py-4">
                            <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                            <p class="text-muted">कोई ऑर्डर नहीं मिला</p>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-hover mb-0">
                                <thead class="bg-light">
                                    <tr>
                                        <th>ऑर्डर #</th>
                                        <th>ग्राहक</th>
                                        <th>सेवा</th>
                                        <th>राशि</th>
                                        <th>स्थिति</th>
                                        <th>कार्य</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($recent_orders as $order_item): ?>
                                        <tr>
                                            <td>
                                                <strong class="text-primary"><?php echo htmlspecialchars($order_item['order_number']); ?></strong>
                                                <br>
                                                <small class="text-muted"><?php echo date('d/m/Y', strtotime($order_item['created_at'])); ?></small>
                                            </td>
                                            <td>
                                                <?php echo htmlspecialchars($order_item['customer_name']); ?>
                                                <br>
                                                <small class="text-muted"><?php echo htmlspecialchars($order_item['customer_email']); ?></small>
                                            </td>
                                            <td><?php echo htmlspecialchars($order_item['service_name_hindi']); ?></td>
                                            <td>
                                                <?php if ($order_item['total_amount'] > 0): ?>
                                                    <strong>₹<?php echo number_format($order_item['total_amount']); ?></strong>
                                                <?php else: ?>
                                                    <span class="text-muted">कस्टम</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <?php
                                                $status_classes = [
                                                    'pending' => 'warning',
                                                    'confirmed' => 'info',
                                                    'in_progress' => 'primary',
                                                    'completed' => 'success',
                                                    'cancelled' => 'danger'
                                                ];
                                                $status_labels = [
                                                    'pending' => 'पेंडिंग',
                                                    'confirmed' => 'पुष्ट',
                                                    'in_progress' => 'प्रगति में',
                                                    'completed' => 'पूर्ण',
                                                    'cancelled' => 'रद्द'
                                                ];
                                                $status_class = $status_classes[$order_item['status']] ?? 'secondary';
                                                $status_label = $status_labels[$order_item['status']] ?? $order_item['status'];
                                                ?>
                                                <span class="badge bg-<?php echo $status_class; ?>">
                                                    <?php echo $status_label; ?>
                                                </span>
                                            </td>
                                            <td>
                                                <div class="btn-group btn-group-sm">
                                                    <a href="order-details.php?id=<?php echo $order_item['id']; ?>" 
                                                       class="btn btn-outline-primary btn-sm">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    <a href="order-edit.php?id=<?php echo $order_item['id']; ?>" 
                                                       class="btn btn-outline-success btn-sm">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Quick Actions & Recent Users -->
        <div class="col-lg-4">
            <!-- Quick Actions -->
            <div class="card shadow-lg border-0 mb-4">
                <div class="card-header bg-dark text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-bolt me-2"></i>
                        त्वरित कार्य
                    </h5>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="orders.php?status=pending" class="btn btn-warning">
                            <i class="fas fa-clock me-2"></i>
                            पेंडिंग ऑर्डर (<?php echo $order_stats['pending_orders']; ?>)
                        </a>
                        <a href="order-create.php" class="btn btn-primary">
                            <i class="fas fa-plus me-2"></i>
                            नया ऑर्डर बनाएं
                        </a>
                        <a href="users.php" class="btn btn-info">
                            <i class="fas fa-users me-2"></i>
                            यूजर प्रबंधन
                        </a>
                        <a href="services.php" class="btn btn-success">
                            <i class="fas fa-cogs me-2"></i>
                            सेवा प्रबंधन
                        </a>
                        <a href="reports.php" class="btn btn-secondary">
                            <i class="fas fa-chart-bar me-2"></i>
                            रिपोर्ट्स
                        </a>
                    </div>
                </div>
            </div>

            <!-- Recent Users -->
            <div class="card shadow-lg border-0">
                <div class="card-header bg-white">
                    <div class="row align-items-center">
                        <div class="col">
                            <h5 class="mb-0">
                                <i class="fas fa-user-plus me-2"></i>
                                नए यूजर
                            </h5>
                        </div>
                        <div class="col-auto">
                            <a href="users.php" class="btn btn-outline-primary btn-sm">
                                सभी देखें
                            </a>
                        </div>
                    </div>
                </div>
                <div class="card-body p-0">
                    <?php if (empty($recent_users)): ?>
                        <div class="text-center py-4">
                            <i class="fas fa-users fa-2x text-muted mb-2"></i>
                            <p class="text-muted mb-0">कोई नया यूजर नहीं</p>
                        </div>
                    <?php else: ?>
                        <div class="list-group list-group-flush">
                            <?php foreach ($recent_users as $user_item): ?>
                                <div class="list-group-item">
                                    <div class="d-flex align-items-center">
                                        <div class="avatar bg-primary text-white rounded-circle me-3">
                                            <?php echo strtoupper(substr($user_item['full_name'], 0, 1)); ?>
                                        </div>
                                        <div class="flex-grow-1">
                                            <h6 class="mb-1"><?php echo htmlspecialchars($user_item['full_name']); ?></h6>
                                            <small class="text-muted"><?php echo htmlspecialchars($user_item['email']); ?></small>
                                            <br>
                                            <small class="text-muted"><?php echo date('d/m/Y', strtotime($user_item['created_at'])); ?></small>
                                        </div>
                                        <div>
                                            <span class="badge bg-<?php echo $user_item['status'] === 'active' ? 'success' : 'warning'; ?>">
                                                <?php echo $user_item['status'] === 'active' ? 'सक्रिय' : 'निष्क्रिय'; ?>
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- Monthly Revenue Chart -->
    <?php if (!empty($monthly_data)): ?>
    <div class="row mt-4">
        <div class="col-12">
            <div class="card shadow-lg border-0">
                <div class="card-header bg-white">
                    <h5 class="mb-0">
                        <i class="fas fa-chart-line me-2"></i>
                        मासिक रेवेन्यू ट्रेंड
                    </h5>
                </div>
                <div class="card-body">
                    <canvas id="revenueChart" height="100"></canvas>
                </div>
            </div>
        </div>
    </div>
    <?php endif; ?>
</div>

<style>
.stat-icon {
    width: 60px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.avatar {
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
}

.table th {
    border-top: none;
    font-weight: 600;
    color: #495057;
    font-size: 0.875rem;
}

.btn-group-sm .btn {
    padding: 0.25rem 0.5rem;
}

@media (max-width: 768px) {
    .container-fluid {
        padding-left: 15px;
        padding-right: 15px;
    }
    
    .table-responsive {
        font-size: 0.875rem;
    }
}
</style>

<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// Revenue Chart
<?php if (!empty($monthly_data)): ?>
const revenueData = <?php echo json_encode(array_reverse($monthly_data)); ?>;
const ctx = document.getElementById('revenueChart').getContext('2d');

const chart = new Chart(ctx, {
    type: 'line',
    data: {
        labels: revenueData.map(item => {
            const months = ['जन', 'फर', 'मार', 'अप्र', 'मई', 'जून', 'जुल', 'अग', 'सित', 'अक्ट', 'नव', 'दिस'];
            return months[item.month - 1] + ' ' + item.year;
        }),
        datasets: [{
            label: 'रेवेन्यू (₹)',
            data: revenueData.map(item => item.revenue),
            borderColor: '#667eea',
            backgroundColor: 'rgba(102, 126, 234, 0.1)',
            borderWidth: 3,
            fill: true,
            tension: 0.4
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                display: false
            }
        },
        scales: {
            y: {
                beginAtZero: true,
                ticks: {
                    callback: function(value) {
                        return '₹' + value.toLocaleString();
                    }
                }
            }
        }
    }
});
<?php endif; ?>

// Auto-refresh dashboard every 60 seconds
setInterval(function() {
    location.reload();
}, 60000);
</script>

<?php include '../includes/footer.php'; ?>
