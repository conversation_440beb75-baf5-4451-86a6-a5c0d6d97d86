<?php
require_once 'config/database.php';

class Blog {
    private $conn;
    private $table_name = "blog_posts";
    
    public $id;
    public $title;
    public $title_hindi;
    public $slug;
    public $content;
    public $content_hindi;
    public $excerpt;
    public $excerpt_hindi;
    public $featured_image;
    public $category_id;
    public $author_id;
    public $status;
    public $meta_title;
    public $meta_description;
    public $tags;
    public $view_count;
    public $published_at;
    public $created_at;
    public $updated_at;
    
    public function __construct() {
        $this->conn = Config::getDB();
    }
    
    // Create new blog post
    public function create() {
        $query = "INSERT INTO " . $this->table_name . " 
                 (title, title_hindi, slug, content, content_hindi, excerpt, excerpt_hindi, 
                  featured_image, category_id, author_id, status, meta_title, meta_description, tags) 
                 VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
        
        $stmt = $this->conn->prepare($query);
        
        // Generate slug if not provided
        if (empty($this->slug)) {
            $this->slug = $this->generateSlug($this->title_hindi);
        }
        
        // Generate excerpt if not provided
        if (empty($this->excerpt_hindi)) {
            $this->excerpt_hindi = $this->generateExcerpt($this->content_hindi);
        }
        
        $stmt->bindParam(1, $this->title);
        $stmt->bindParam(2, $this->title_hindi);
        $stmt->bindParam(3, $this->slug);
        $stmt->bindParam(4, $this->content);
        $stmt->bindParam(5, $this->content_hindi);
        $stmt->bindParam(6, $this->excerpt);
        $stmt->bindParam(7, $this->excerpt_hindi);
        $stmt->bindParam(8, $this->featured_image);
        $stmt->bindParam(9, $this->category_id);
        $stmt->bindParam(10, $this->author_id);
        $stmt->bindParam(11, $this->status);
        $stmt->bindParam(12, $this->meta_title);
        $stmt->bindParam(13, $this->meta_description);
        $stmt->bindParam(14, $this->tags);
        
        if ($stmt->execute()) {
            $this->id = $this->conn->lastInsertId();
            
            // Set published_at if status is published
            if ($this->status === 'published') {
                $this->setPublishedAt();
            }
            
            return true;
        }
        return false;
    }
    
    // Get blog post by ID
    public function getById($id) {
        $query = "SELECT bp.*, bc.name as category_name, bc.name_hindi as category_name_hindi,
                         u.full_name as author_name
                 FROM " . $this->table_name . " bp
                 LEFT JOIN blog_categories bc ON bp.category_id = bc.id
                 LEFT JOIN users u ON bp.author_id = u.id
                 WHERE bp.id = ?";
        
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(1, $id);
        $stmt->execute();
        
        if ($stmt->rowCount() > 0) {
            $row = $stmt->fetch(PDO::FETCH_ASSOC);
            $this->populateFromArray($row);
            return $row;
        }
        return false;
    }
    
    // Get blog post by slug
    public function getBySlug($slug) {
        $query = "SELECT bp.*, bc.name as category_name, bc.name_hindi as category_name_hindi,
                         u.full_name as author_name
                 FROM " . $this->table_name . " bp
                 LEFT JOIN blog_categories bc ON bp.category_id = bc.id
                 LEFT JOIN users u ON bp.author_id = u.id
                 WHERE bp.slug = ? AND bp.status = 'published'";
        
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(1, $slug);
        $stmt->execute();
        
        if ($stmt->rowCount() > 0) {
            $row = $stmt->fetch(PDO::FETCH_ASSOC);
            $this->populateFromArray($row);
            
            // Increment view count
            $this->incrementViewCount();
            
            return $row;
        }
        return false;
    }
    
    // Get all published blog posts
    public function getAllPublished($limit = 10, $offset = 0, $category_id = null, $search = '') {
        $query = "SELECT bp.*, bc.name as category_name, bc.name_hindi as category_name_hindi,
                         u.full_name as author_name
                 FROM " . $this->table_name . " bp
                 LEFT JOIN blog_categories bc ON bp.category_id = bc.id
                 LEFT JOIN users u ON bp.author_id = u.id
                 WHERE bp.status = 'published'";
        
        $params = [];
        
        if ($category_id) {
            $query .= " AND bp.category_id = ?";
            $params[] = $category_id;
        }
        
        if (!empty($search)) {
            $query .= " AND (bp.title_hindi LIKE ? OR bp.content_hindi LIKE ? OR bp.tags LIKE ?)";
            $search_term = '%' . $search . '%';
            $params[] = $search_term;
            $params[] = $search_term;
            $params[] = $search_term;
        }
        
        $query .= " ORDER BY bp.published_at DESC LIMIT ? OFFSET ?";
        $params[] = $limit;
        $params[] = $offset;
        
        $stmt = $this->conn->prepare($query);
        $stmt->execute($params);
        
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    // Get all posts (admin)
    public function getAll($limit = 20, $offset = 0, $status = '') {
        $query = "SELECT bp.*, bc.name as category_name, bc.name_hindi as category_name_hindi,
                         u.full_name as author_name
                 FROM " . $this->table_name . " bp
                 LEFT JOIN blog_categories bc ON bp.category_id = bc.id
                 LEFT JOIN users u ON bp.author_id = u.id
                 WHERE 1=1";
        
        $params = [];
        
        if (!empty($status)) {
            $query .= " AND bp.status = ?";
            $params[] = $status;
        }
        
        $query .= " ORDER BY bp.created_at DESC LIMIT ? OFFSET ?";
        $params[] = $limit;
        $params[] = $offset;
        
        $stmt = $this->conn->prepare($query);
        $stmt->execute($params);
        
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    // Get popular posts
    public function getPopularPosts($limit = 5) {
        $query = "SELECT bp.*, bc.name_hindi as category_name_hindi
                 FROM " . $this->table_name . " bp
                 LEFT JOIN blog_categories bc ON bp.category_id = bc.id
                 WHERE bp.status = 'published'
                 ORDER BY bp.view_count DESC, bp.published_at DESC
                 LIMIT ?";
        
        $stmt = $this->conn->prepare($query);
        $stmt->execute([$limit]);
        
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    // Get recent posts
    public function getRecentPosts($limit = 5) {
        $query = "SELECT bp.*, bc.name_hindi as category_name_hindi
                 FROM " . $this->table_name . " bp
                 LEFT JOIN blog_categories bc ON bp.category_id = bc.id
                 WHERE bp.status = 'published'
                 ORDER BY bp.published_at DESC
                 LIMIT ?";
        
        $stmt = $this->conn->prepare($query);
        $stmt->execute([$limit]);
        
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    // Get related posts
    public function getRelatedPosts($post_id, $category_id, $limit = 3) {
        $query = "SELECT bp.*, bc.name_hindi as category_name_hindi
                 FROM " . $this->table_name . " bp
                 LEFT JOIN blog_categories bc ON bp.category_id = bc.id
                 WHERE bp.status = 'published' 
                 AND bp.id != ? 
                 AND bp.category_id = ?
                 ORDER BY bp.published_at DESC
                 LIMIT ?";
        
        $stmt = $this->conn->prepare($query);
        $stmt->execute([$post_id, $category_id, $limit]);
        
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    // Update blog post
    public function update() {
        $query = "UPDATE " . $this->table_name . " 
                 SET title = ?, title_hindi = ?, slug = ?, content = ?, content_hindi = ?, 
                     excerpt = ?, excerpt_hindi = ?, featured_image = ?, category_id = ?, 
                     status = ?, meta_title = ?, meta_description = ?, tags = ?, 
                     updated_at = CURRENT_TIMESTAMP
                 WHERE id = ?";
        
        $stmt = $this->conn->prepare($query);
        
        // Generate slug if not provided
        if (empty($this->slug)) {
            $this->slug = $this->generateSlug($this->title_hindi);
        }
        
        // Generate excerpt if not provided
        if (empty($this->excerpt_hindi)) {
            $this->excerpt_hindi = $this->generateExcerpt($this->content_hindi);
        }
        
        $stmt->bindParam(1, $this->title);
        $stmt->bindParam(2, $this->title_hindi);
        $stmt->bindParam(3, $this->slug);
        $stmt->bindParam(4, $this->content);
        $stmt->bindParam(5, $this->content_hindi);
        $stmt->bindParam(6, $this->excerpt);
        $stmt->bindParam(7, $this->excerpt_hindi);
        $stmt->bindParam(8, $this->featured_image);
        $stmt->bindParam(9, $this->category_id);
        $stmt->bindParam(10, $this->status);
        $stmt->bindParam(11, $this->meta_title);
        $stmt->bindParam(12, $this->meta_description);
        $stmt->bindParam(13, $this->tags);
        $stmt->bindParam(14, $this->id);
        
        if ($stmt->execute()) {
            // Set published_at if status changed to published
            if ($this->status === 'published') {
                $this->setPublishedAt();
            }
            return true;
        }
        return false;
    }
    
    // Delete blog post
    public function delete() {
        $query = "DELETE FROM " . $this->table_name . " WHERE id = ?";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(1, $this->id);
        return $stmt->execute();
    }
    
    // Generate slug from title
    private function generateSlug($title) {
        // Convert to lowercase and replace spaces with hyphens
        $slug = strtolower(trim($title));
        $slug = preg_replace('/[^a-z0-9\s-]/', '', $slug);
        $slug = preg_replace('/[\s-]+/', '-', $slug);
        $slug = trim($slug, '-');
        
        // Ensure uniqueness
        $original_slug = $slug;
        $counter = 1;
        
        while ($this->slugExists($slug)) {
            $slug = $original_slug . '-' . $counter;
            $counter++;
        }
        
        return $slug;
    }
    
    // Check if slug exists
    private function slugExists($slug) {
        $query = "SELECT id FROM " . $this->table_name . " WHERE slug = ?";
        if ($this->id) {
            $query .= " AND id != ?";
        }
        
        $stmt = $this->conn->prepare($query);
        if ($this->id) {
            $stmt->execute([$slug, $this->id]);
        } else {
            $stmt->execute([$slug]);
        }
        
        return $stmt->rowCount() > 0;
    }
    
    // Generate excerpt from content
    private function generateExcerpt($content, $length = 150) {
        $content = strip_tags($content);
        if (strlen($content) <= $length) {
            return $content;
        }
        
        $excerpt = substr($content, 0, $length);
        $last_space = strrpos($excerpt, ' ');
        
        if ($last_space !== false) {
            $excerpt = substr($excerpt, 0, $last_space);
        }
        
        return $excerpt . '...';
    }
    
    // Set published date
    private function setPublishedAt() {
        if ($this->id && empty($this->published_at)) {
            $query = "UPDATE " . $this->table_name . " SET published_at = NOW() WHERE id = ? AND published_at IS NULL";
            $stmt = $this->conn->prepare($query);
            $stmt->execute([$this->id]);
        }
    }
    
    // Increment view count
    private function incrementViewCount() {
        if ($this->id) {
            $query = "UPDATE " . $this->table_name . " SET view_count = view_count + 1 WHERE id = ?";
            $stmt = $this->conn->prepare($query);
            $stmt->execute([$this->id]);
        }
    }
    
    // Populate object from array
    private function populateFromArray($data) {
        $this->id = $data['id'];
        $this->title = $data['title'];
        $this->title_hindi = $data['title_hindi'];
        $this->slug = $data['slug'];
        $this->content = $data['content'];
        $this->content_hindi = $data['content_hindi'];
        $this->excerpt = $data['excerpt'];
        $this->excerpt_hindi = $data['excerpt_hindi'];
        $this->featured_image = $data['featured_image'];
        $this->category_id = $data['category_id'];
        $this->author_id = $data['author_id'];
        $this->status = $data['status'];
        $this->meta_title = $data['meta_title'];
        $this->meta_description = $data['meta_description'];
        $this->tags = $data['tags'];
        $this->view_count = $data['view_count'];
        $this->published_at = $data['published_at'];
        $this->created_at = $data['created_at'];
        $this->updated_at = $data['updated_at'];
    }
    
    // Get blog statistics
    public function getBlogStats() {
        $stats = [];
        
        // Total posts
        $query = "SELECT COUNT(*) as total FROM " . $this->table_name;
        $stmt = $this->conn->prepare($query);
        $stmt->execute();
        $stats['total_posts'] = $stmt->fetch()['total'];
        
        // Published posts
        $query = "SELECT COUNT(*) as published FROM " . $this->table_name . " WHERE status = 'published'";
        $stmt = $this->conn->prepare($query);
        $stmt->execute();
        $stats['published_posts'] = $stmt->fetch()['published'];
        
        // Draft posts
        $query = "SELECT COUNT(*) as draft FROM " . $this->table_name . " WHERE status = 'draft'";
        $stmt = $this->conn->prepare($query);
        $stmt->execute();
        $stats['draft_posts'] = $stmt->fetch()['draft'];
        
        // Total views
        $query = "SELECT SUM(view_count) as total_views FROM " . $this->table_name . " WHERE status = 'published'";
        $stmt = $this->conn->prepare($query);
        $stmt->execute();
        $stats['total_views'] = $stmt->fetch()['total_views'] ?? 0;
        
        return $stats;
    }
    
    // Get categories
    public function getCategories() {
        $query = "SELECT * FROM blog_categories WHERE is_active = 1 ORDER BY name_hindi";
        $stmt = $this->conn->prepare($query);
        $stmt->execute();
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    // Search posts
    public function searchPosts($search_term, $limit = 10, $offset = 0) {
        $query = "SELECT bp.*, bc.name_hindi as category_name_hindi, u.full_name as author_name
                 FROM " . $this->table_name . " bp
                 LEFT JOIN blog_categories bc ON bp.category_id = bc.id
                 LEFT JOIN users u ON bp.author_id = u.id
                 WHERE bp.status = 'published' 
                 AND (bp.title_hindi LIKE ? OR bp.content_hindi LIKE ? OR bp.tags LIKE ?)
                 ORDER BY bp.published_at DESC
                 LIMIT ? OFFSET ?";
        
        $search_term = '%' . $search_term . '%';
        $stmt = $this->conn->prepare($query);
        $stmt->execute([$search_term, $search_term, $search_term, $limit, $offset]);
        
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
}
?>
