<?php
require_once 'config/database.php';
require_once 'classes/User.php';
require_once 'classes/Order.php';

// Check if user is logged in
if (!User::isLoggedIn()) {
    $_SESSION['redirect_after_login'] = $_SERVER['REQUEST_URI'];
    header('Location: auth/login.php');
    exit();
}

$page_title = 'ऑर्डर करें - वेब डेवलपमेंट सेवाएं';
$meta_description = 'अपनी वेब डेवलपमेंट सेवा का ऑर्डर करें। सुरक्षित पेमेंट के साथ तुरंत ऑर्डर प्लेसमेंट।';

// Get service ID from URL
$service_id = $_GET['service'] ?? 1;

// Get service details
try {
    $db = Config::getDB();
    $stmt = $db->prepare("SELECT * FROM services WHERE id = ? AND is_active = 1");
    $stmt->execute([$service_id]);
    $service = $stmt->fetch();
    
    if (!$service) {
        $_SESSION['error_message'] = 'सेवा नहीं मिली।';
        header('Location: services.php');
        exit();
    }
} catch(Exception $e) {
    $_SESSION['error_message'] = 'डेटाबेस त्रुटि।';
    header('Location: services.php');
    exit();
}

$error_messages = [];
$success_message = '';

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    // Verify CSRF token
    if (!isset($_POST['csrf_token']) || !verifyCsrfToken($_POST['csrf_token'])) {
        $error_messages[] = 'सुरक्षा त्रुटि। कृपया पुनः प्रयास करें।';
    } else {
        $client_requirements = sanitizeInput($_POST['client_requirements']);
        $agree_terms = isset($_POST['agree_terms']);
        
        if (empty($client_requirements)) {
            $error_messages[] = 'कृपया अपनी आवश्यकताएं बताएं।';
        }
        
        if (!$agree_terms) {
            $error_messages[] = 'कृपया सेवा की शर्तों से सहमति दें।';
        }
        
        if (empty($error_messages)) {
            $order = new Order();
            $order->user_id = $_SESSION['user_id'];
            $order->service_id = $service_id;
            $order->status = 'pending';
            $order->total_amount = $service['price'];
            $order->payment_status = 'pending';
            $order->client_requirements = $client_requirements;
            
            if ($order->create()) {
                // Redirect to payment page
                $_SESSION['success_message'] = 'ऑर्डर सफलतापूर्वक बनाया गया! अब पेमेंट करें।';
                header('Location: payment.php?order=' . $order->order_number);
                exit();
            } else {
                $error_messages[] = 'ऑर्डर बनाने में त्रुटि। कृपया बाद में पुनः प्रयास करें।';
            }
        }
    }
}

include 'includes/header.php';
?>

<div class="container mt-5 pt-5">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <!-- Service Details Card -->
            <div class="card shadow-lg border-0 mb-4">
                <div class="card-header bg-primary text-white py-4">
                    <h3 class="mb-0">
                        <i class="fas fa-shopping-cart me-2"></i>
                        ऑर्डर करें
                    </h3>
                </div>
                <div class="card-body p-4">
                    <!-- Selected Service -->
                    <div class="selected-service mb-4">
                        <h4 class="text-primary mb-3">चयनित सेवा:</h4>
                        <div class="service-info bg-light p-4 rounded">
                            <div class="row align-items-center">
                                <div class="col-md-8">
                                    <h5 class="mb-2"><?php echo htmlspecialchars($service['name_hindi']); ?></h5>
                                    <p class="text-muted mb-0"><?php echo htmlspecialchars($service['description_hindi']); ?></p>
                                </div>
                                <div class="col-md-4 text-md-end">
                                    <div class="price-display">
                                        <?php if ($service['price'] > 0): ?>
                                            <span class="h3 text-success fw-bold">₹<?php echo number_format($service['price']); ?></span>
                                            <small class="d-block text-muted">एक बार भुगतान</small>
                                        <?php else: ?>
                                            <span class="h5 text-info fw-bold">मूल्य अनुरोध पर</span>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Service Features -->
                    <div class="service-features mb-4">
                        <h5 class="mb-3">इस पैकेज में शामिल:</h5>
                        <div class="row">
                            <?php 
                            $features = json_decode($service['features_hindi'], true);
                            if ($features):
                                foreach ($features as $feature):
                            ?>
                                <div class="col-md-6 mb-2">
                                    <i class="fas fa-check text-success me-2"></i>
                                    <?php echo htmlspecialchars($feature); ?>
                                </div>
                            <?php 
                                endforeach;
                            endif;
                            ?>
                        </div>
                    </div>

                    <?php if (!empty($error_messages)): ?>
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-circle me-2"></i>
                            <ul class="mb-0">
                                <?php foreach ($error_messages as $error): ?>
                                    <li><?php echo $error; ?></li>
                                <?php endforeach; ?>
                            </ul>
                        </div>
                    <?php endif; ?>

                    <!-- Order Form -->
                    <form method="POST" action="" id="orderForm">
                        <input type="hidden" name="csrf_token" value="<?php echo $_SESSION['csrf_token']; ?>">
                        
                        <div class="mb-4">
                            <label for="client_requirements" class="form-label">
                                <i class="fas fa-clipboard-list me-1"></i>
                                आपकी आवश्यकताएं और विवरण *
                            </label>
                            <textarea class="form-control" id="client_requirements" name="client_requirements" 
                                      rows="6" placeholder="कृपया अपनी वेबसाइट की आवश्यकताओं का विस्तार से वर्णन करें..." required><?php echo htmlspecialchars($_POST['client_requirements'] ?? ''); ?></textarea>
                            <div class="form-text">
                                कृपया बताएं: वेबसाइट का उद्देश्य, पसंदीदा रंग, कंटेंट, विशेष फीचर्स, आदि।
                            </div>
                        </div>

                        <!-- Customer Information Display -->
                        <div class="customer-info mb-4">
                            <h5 class="mb-3">ग्राहक जानकारी:</h5>
                            <div class="bg-light p-3 rounded">
                                <div class="row">
                                    <div class="col-md-6">
                                        <strong>नाम:</strong> <?php echo htmlspecialchars($_SESSION['full_name']); ?>
                                    </div>
                                    <div class="col-md-6">
                                        <strong>ईमेल:</strong> <?php echo htmlspecialchars($_SESSION['email']); ?>
                                    </div>
                                </div>
                                <div class="mt-2">
                                    <small class="text-muted">
                                        <i class="fas fa-info-circle me-1"></i>
                                        यदि आपको अपनी जानकारी अपडेट करनी है, तो 
                                        <a href="client/profile.php">प्रोफाइल पेज</a> पर जाएं।
                                    </small>
                                </div>
                            </div>
                        </div>

                        <!-- Terms and Conditions -->
                        <div class="mb-4">
                            <div class="form-check">
                                <input type="checkbox" class="form-check-input" id="agree_terms" name="agree_terms" required>
                                <label class="form-check-label" for="agree_terms">
                                    मैं <a href="legal/terms-of-service.php" target="_blank">सेवा की शर्तों</a>, 
                                    <a href="legal/privacy-policy.php" target="_blank">गोपनीयता नीति</a> और 
                                    <a href="legal/refund-policy.php" target="_blank">रिफंड नीति</a> से सहमत हूं *
                                </label>
                            </div>
                        </div>

                        <!-- Order Summary -->
                        <div class="order-summary bg-primary text-white p-4 rounded mb-4">
                            <h5 class="mb-3">ऑर्डर सारांश:</h5>
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <strong><?php echo htmlspecialchars($service['name_hindi']); ?></strong>
                                </div>
                                <div>
                                    <?php if ($service['price'] > 0): ?>
                                        <strong class="h4">₹<?php echo number_format($service['price']); ?></strong>
                                    <?php else: ?>
                                        <strong>मूल्य अनुरोध पर</strong>
                                    <?php endif; ?>
                                </div>
                            </div>
                            <?php if ($service['price'] > 0): ?>
                                <hr class="my-3">
                                <div class="d-flex justify-content-between">
                                    <strong>कुल राशि:</strong>
                                    <strong class="h4">₹<?php echo number_format($service['price']); ?></strong>
                                </div>
                            <?php endif; ?>
                        </div>

                        <!-- Submit Button -->
                        <div class="d-grid gap-2">
                            <?php if ($service['price'] > 0): ?>
                                <button type="submit" class="btn btn-success btn-lg">
                                    <i class="fas fa-credit-card me-2"></i>
                                    ऑर्डर करें और पेमेंट करें
                                </button>
                            <?php else: ?>
                                <button type="submit" class="btn btn-info btn-lg">
                                    <i class="fas fa-paper-plane me-2"></i>
                                    कस्टम क्वोट के लिए भेजें
                                </button>
                            <?php endif; ?>
                            <a href="services.php" class="btn btn-outline-secondary">
                                <i class="fas fa-arrow-left me-2"></i>
                                वापस सेवाओं पर जाएं
                            </a>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Additional Information -->
            <div class="row g-4">
                <div class="col-md-6">
                    <div class="card border-0 shadow-sm">
                        <div class="card-body text-center">
                            <i class="fas fa-shield-alt fa-3x text-success mb-3"></i>
                            <h5>सुरक्षित पेमेंट</h5>
                            <p class="text-muted">Razorpay के माध्यम से 100% सुरक्षित पेमेंट</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card border-0 shadow-sm">
                        <div class="card-body text-center">
                            <i class="fas fa-headset fa-3x text-primary mb-3"></i>
                            <h5>24/7 सहायता</h5>
                            <p class="text-muted">किसी भी समस्या के लिए हमारी टीम उपलब्ध है</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Form validation
document.getElementById('orderForm').addEventListener('submit', function(e) {
    const requirements = document.getElementById('client_requirements').value.trim();
    const agreeTerms = document.getElementById('agree_terms').checked;
    
    if (requirements.length < 50) {
        e.preventDefault();
        alert('कृपया अपनी आवश्यकताओं का विस्तार से वर्णन करें (कम से कम 50 अक्षर)।');
        return false;
    }
    
    if (!agreeTerms) {
        e.preventDefault();
        alert('कृपया सेवा की शर्तों से सहमति दें।');
        return false;
    }
    
    // Show loading state
    const submitBtn = this.querySelector('button[type="submit"]');
    const originalText = submitBtn.innerHTML;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>प्रोसेसिंग...';
    submitBtn.disabled = true;
    
    // Re-enable button after 10 seconds (in case of issues)
    setTimeout(() => {
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;
    }, 10000);
});

// Character counter for requirements
document.getElementById('client_requirements').addEventListener('input', function() {
    const length = this.value.length;
    const minLength = 50;
    
    // Create or update character counter
    let counter = document.getElementById('char-counter');
    if (!counter) {
        counter = document.createElement('div');
        counter.id = 'char-counter';
        counter.className = 'form-text';
        this.parentNode.appendChild(counter);
    }
    
    if (length < minLength) {
        counter.innerHTML = `<i class="fas fa-info-circle me-1"></i>${length}/${minLength} अक्षर (कम से कम ${minLength} अक्षर आवश्यक)`;
        counter.className = 'form-text text-warning';
    } else {
        counter.innerHTML = `<i class="fas fa-check-circle me-1"></i>${length} अक्षर - बहुत अच्छा!`;
        counter.className = 'form-text text-success';
    }
});

// Auto-save draft (optional feature)
let saveTimeout;
document.getElementById('client_requirements').addEventListener('input', function() {
    clearTimeout(saveTimeout);
    saveTimeout = setTimeout(() => {
        localStorage.setItem('order_draft_' + <?php echo $service_id; ?>, this.value);
    }, 2000);
});

// Load draft on page load
window.addEventListener('load', function() {
    const draft = localStorage.getItem('order_draft_' + <?php echo $service_id; ?>);
    const textarea = document.getElementById('client_requirements');
    if (draft && !textarea.value) {
        textarea.value = draft;
        textarea.dispatchEvent(new Event('input'));
    }
});
</script>

<?php include 'includes/footer.php'; ?>
