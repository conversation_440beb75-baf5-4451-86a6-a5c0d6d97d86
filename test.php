<?php
// Quick test page to check all functionality
require_once 'config/database.php';
require_once 'includes/functions.php';

echo "<h1>🧪 Website Functionality Test</h1>";
echo "<style>body{font-family:Arial;margin:20px;} .success{color:green;} .error{color:red;} .info{color:blue;}</style>";

// Test 1: Database Connection
echo "<h2>1. Database Connection Test</h2>";
try {
    $db = Config::getDB();
    echo "<p class='success'>✅ Database connection successful!</p>";
    
    // Check if tables exist
    $tables = ['users', 'services', 'contact_inquiries', 'orders', 'blog_categories', 'blog_posts', 'portfolio', 'newsletter_subscribers'];
    foreach ($tables as $table) {
        $stmt = $db->query("SHOW TABLES LIKE '$table'");
        if ($stmt->rowCount() > 0) {
            echo "<p class='success'>✅ Table '$table' exists</p>";
        } else {
            echo "<p class='error'>❌ Table '$table' missing</p>";
        }
    }
} catch (Exception $e) {
    echo "<p class='error'>❌ Database connection failed: " . $e->getMessage() . "</p>";
    echo "<p class='info'>💡 Please run <a href='setup_database.php'>setup_database.php</a> first</p>";
}

// Test 2: Check if admin user exists
echo "<h2>2. Admin User Test</h2>";
try {
    $stmt = $db->prepare("SELECT * FROM users WHERE role = 'admin' LIMIT 1");
    $stmt->execute();
    $admin = $stmt->fetch();
    
    if ($admin) {
        echo "<p class='success'>✅ Admin user exists: " . $admin['username'] . "</p>";
        echo "<p class='info'>💡 Login: username='" . $admin['username'] . "', password='admin123'</p>";
    } else {
        echo "<p class='error'>❌ No admin user found</p>";
    }
} catch (Exception $e) {
    echo "<p class='error'>❌ Error checking admin user: " . $e->getMessage() . "</p>";
}

// Test 3: Check services
echo "<h2>3. Services Test</h2>";
try {
    $stmt = $db->query("SELECT COUNT(*) as count FROM services");
    $count = $stmt->fetch()['count'];
    
    if ($count > 0) {
        echo "<p class='success'>✅ $count services found</p>";
        
        $stmt = $db->query("SELECT name_hindi, price FROM services ORDER BY id");
        while ($service = $stmt->fetch()) {
            echo "<p class='info'>📦 " . $service['name_hindi'] . " - ₹" . number_format($service['price']) . "</p>";
        }
    } else {
        echo "<p class='error'>❌ No services found</p>";
    }
} catch (Exception $e) {
    echo "<p class='error'>❌ Error checking services: " . $e->getMessage() . "</p>";
}

// Test 4: File Structure
echo "<h2>4. File Structure Test</h2>";
$required_files = [
    'index.php' => 'Homepage',
    'services.php' => 'Services page',
    'about.php' => 'About page',
    'contact.php' => 'Contact page',
    'blog.php' => 'Blog page',
    'portfolio.php' => 'Portfolio page',
    'auth/login.php' => 'Login page',
    'auth/register.php' => 'Registration page',
    'includes/header.php' => 'Header include',
    'includes/footer.php' => 'Footer include',
    'includes/functions.php' => 'Functions include',
    'classes/User.php' => 'User class',
    'config/database.php' => 'Database config'
];

foreach ($required_files as $file => $description) {
    if (file_exists($file)) {
        echo "<p class='success'>✅ $description ($file)</p>";
    } else {
        echo "<p class='error'>❌ Missing: $description ($file)</p>";
    }
}

// Test 5: Configuration
echo "<h2>5. Configuration Test</h2>";
if (defined('Config::SITE_NAME_HINDI')) {
    echo "<p class='success'>✅ Site configuration loaded</p>";
    echo "<p class='info'>🏢 Site Name: " . Config::SITE_NAME_HINDI . "</p>";
    echo "<p class='info'>🌐 Site URL: " . Config::SITE_URL . "</p>";
} else {
    echo "<p class='error'>❌ Configuration not loaded properly</p>";
}

// Test 6: Session
echo "<h2>6. Session Test</h2>";
if (session_status() === PHP_SESSION_ACTIVE) {
    echo "<p class='success'>✅ Sessions are working</p>";
    echo "<p class='info'>📝 Session ID: " . session_id() . "</p>";
} else {
    echo "<p class='error'>❌ Sessions not working</p>";
}

// Test 7: Functions
echo "<h2>7. Functions Test</h2>";
if (function_exists('sanitizeInput')) {
    echo "<p class='success'>✅ Custom functions loaded</p>";
    echo "<p class='info'>🧹 sanitizeInput test: " . sanitizeInput('<script>alert("test")</script>') . "</p>";
} else {
    echo "<p class='error'>❌ Custom functions not loaded</p>";
}

// Test 8: Quick Links
echo "<h2>8. Quick Navigation</h2>";
echo "<div style='background:#f5f5f5;padding:20px;border-radius:5px;'>";
echo "<h3>🔗 Important Links:</h3>";
echo "<ul>";
echo "<li><a href='index.php'>🏠 Homepage</a></li>";
echo "<li><a href='services.php'>💼 Services</a></li>";
echo "<li><a href='about.php'>ℹ️ About Us</a></li>";
echo "<li><a href='contact.php'>📞 Contact</a></li>";
echo "<li><a href='blog.php'>📝 Blog</a></li>";
echo "<li><a href='portfolio.php'>🎨 Portfolio</a></li>";
echo "<li><a href='auth/login.php'>🔐 Login</a></li>";
echo "<li><a href='auth/register.php'>📝 Register</a></li>";
echo "</ul>";
echo "</div>";

// Test 9: Database Setup Link
echo "<h2>9. Setup Actions</h2>";
echo "<div style='background:#e7f3ff;padding:20px;border-radius:5px;border-left:4px solid #007bff;'>";
echo "<h3>🛠️ Setup Actions:</h3>";
echo "<ul>";
echo "<li><a href='setup_database.php' style='color:#007bff;font-weight:bold;'>🗄️ Setup Database</a> - Run this first if database is not setup</li>";
echo "<li><a href='auth/login.php' style='color:#28a745;font-weight:bold;'>👤 Admin Login</a> - Username: admin, Password: admin123</li>";
echo "</ul>";
echo "</div>";

// Test 10: System Info
echo "<h2>10. System Information</h2>";
echo "<div style='background:#f8f9fa;padding:15px;border-radius:5px;'>";
echo "<p><strong>PHP Version:</strong> " . PHP_VERSION . "</p>";
echo "<p><strong>Server:</strong> " . $_SERVER['SERVER_SOFTWARE'] . "</p>";
echo "<p><strong>Document Root:</strong> " . $_SERVER['DOCUMENT_ROOT'] . "</p>";
echo "<p><strong>Current Directory:</strong> " . __DIR__ . "</p>";
echo "<p><strong>Current Time:</strong> " . date('Y-m-d H:i:s') . "</p>";
echo "</div>";

echo "<hr>";
echo "<p style='text-align:center;color:#666;'>Test completed at " . date('Y-m-d H:i:s') . "</p>";
?>
