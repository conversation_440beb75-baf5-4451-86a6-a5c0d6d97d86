<?php
require_once 'config/database.php';
require_once 'classes/User.php';

$page_title = 'पोर्टफोलियो - हमारे काम के नमूने';
$meta_description = 'हमारे द्वारा बनाई गई वेबसाइटों और प्रोजेक्ट्स के नमूने देखें। बिजनेस वेबसाइट, ई-कॉमर्स और कस्टम डेवलपमेंट के उदाहरण।';

// Get filter parameters
$project_type = $_GET['type'] ?? '';
$page = max(1, intval($_GET['page'] ?? 1));
$projects_per_page = 9;
$offset = ($page - 1) * $projects_per_page;

// Get portfolio projects
try {
    $db = Config::getDB();
    
    $query = "SELECT * FROM portfolio WHERE 1=1";
    $params = [];
    
    if (!empty($project_type)) {
        $query .= " AND project_type = ?";
        $params[] = $project_type;
    }
    
    $query .= " ORDER BY is_featured DESC, display_order ASC, completion_date DESC LIMIT ? OFFSET ?";
    $params[] = $projects_per_page;
    $params[] = $offset;
    
    $stmt = $db->prepare($query);
    $stmt->execute($params);
    $projects = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Get total count for pagination
    $count_query = "SELECT COUNT(*) as total FROM portfolio WHERE 1=1";
    $count_params = [];
    
    if (!empty($project_type)) {
        $count_query .= " AND project_type = ?";
        $count_params[] = $project_type;
    }
    
    $count_stmt = $db->prepare($count_query);
    $count_stmt->execute($count_params);
    $total_projects = $count_stmt->fetch()['total'];
    $total_pages = ceil($total_projects / $projects_per_page);
    
} catch(Exception $e) {
    $projects = [];
    $total_projects = 0;
    $total_pages = 1;
}

include 'includes/header.php';
?>

<div class="container mt-5 pt-5">
    <!-- Page Header -->
    <div class="row mb-5">
        <div class="col-12">
            <div class="text-center">
                <h1 class="display-4 fw-bold">हमारा पोर्टफोलियो</h1>
                <p class="lead text-muted">हमारे द्वारा बनाए गए प्रोजेक्ट्स और वेबसाइटों के नमूने</p>
            </div>
        </div>
    </div>

    <!-- Filter Tabs -->
    <div class="row mb-4">
        <div class="col-12">
            <ul class="nav nav-pills justify-content-center">
                <li class="nav-item">
                    <a class="nav-link <?php echo empty($project_type) ? 'active' : ''; ?>" 
                       href="portfolio.php">
                        सभी प्रोजेक्ट्स
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link <?php echo $project_type === 'business' ? 'active' : ''; ?>" 
                       href="portfolio.php?type=business">
                        बिजनेस वेबसाइट
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link <?php echo $project_type === 'ecommerce' ? 'active' : ''; ?>" 
                       href="portfolio.php?type=ecommerce">
                        ई-कॉमर्स
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link <?php echo $project_type === 'custom' ? 'active' : ''; ?>" 
                       href="portfolio.php?type=custom">
                        कस्टम डेवलपमेंट
                    </a>
                </li>
            </ul>
        </div>
    </div>

    <!-- Portfolio Grid -->
    <?php if (empty($projects)): ?>
        <div class="text-center py-5">
            <i class="fas fa-briefcase fa-3x text-muted mb-3"></i>
            <h4 class="text-muted">कोई प्रोजेक्ट नहीं मिला</h4>
            <p class="text-muted">इस कैटेगरी में अभी तक कोई प्रोजेक्ट नहीं है।</p>
        </div>
    <?php else: ?>
        <div class="row g-4 mb-5">
            <?php foreach ($projects as $project): ?>
                <?php 
                $images = json_decode($project['image_gallery'], true) ?: [];
                $technologies = json_decode($project['technologies_used'], true) ?: [];
                $main_image = !empty($images) ? $images[0] : 'assets/images/placeholder-project.jpg';
                ?>
                <div class="col-lg-4 col-md-6">
                    <div class="portfolio-item card border-0 shadow-lg h-100">
                        <?php if ($project['is_featured']): ?>
                            <div class="featured-badge">
                                <span class="badge bg-warning text-dark">फीचर्ड</span>
                            </div>
                        <?php endif; ?>
                        
                        <div class="portfolio-image">
                            <img src="<?php echo htmlspecialchars($main_image); ?>" 
                                 class="card-img-top" 
                                 alt="<?php echo htmlspecialchars($project['title_hindi']); ?>"
                                 style="height: 250px; object-fit: cover;">
                            <div class="portfolio-overlay">
                                <div class="portfolio-actions">
                                    <?php if ($project['project_url']): ?>
                                        <a href="<?php echo htmlspecialchars($project['project_url']); ?>" 
                                           target="_blank" 
                                           class="btn btn-light btn-sm me-2">
                                            <i class="fas fa-external-link-alt"></i>
                                        </a>
                                    <?php endif; ?>
                                    <button class="btn btn-primary btn-sm" 
                                            data-bs-toggle="modal" 
                                            data-bs-target="#projectModal<?php echo $project['id']; ?>">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                        
                        <div class="card-body d-flex flex-column">
                            <!-- Project Type Badge -->
                            <div class="mb-2">
                                <?php
                                $type_labels = [
                                    'business' => 'बिजनेस',
                                    'ecommerce' => 'ई-कॉमर्स',
                                    'custom' => 'कस्टम'
                                ];
                                $type_classes = [
                                    'business' => 'primary',
                                    'ecommerce' => 'success',
                                    'custom' => 'info'
                                ];
                                $type_label = $type_labels[$project['project_type']] ?? $project['project_type'];
                                $type_class = $type_classes[$project['project_type']] ?? 'secondary';
                                ?>
                                <span class="badge bg-<?php echo $type_class; ?>">
                                    <?php echo $type_label; ?>
                                </span>
                            </div>
                            
                            <!-- Title -->
                            <h5 class="card-title">
                                <?php echo htmlspecialchars($project['title_hindi']); ?>
                            </h5>
                            
                            <!-- Description -->
                            <p class="card-text text-muted flex-grow-1">
                                <?php echo htmlspecialchars(substr($project['description_hindi'], 0, 100)) . (strlen($project['description_hindi']) > 100 ? '...' : ''); ?>
                            </p>
                            
                            <!-- Technologies -->
                            <?php if (!empty($technologies)): ?>
                                <div class="technologies mb-3">
                                    <?php foreach (array_slice($technologies, 0, 3) as $tech): ?>
                                        <span class="badge bg-light text-dark me-1 mb-1">
                                            <?php echo htmlspecialchars($tech); ?>
                                        </span>
                                    <?php endforeach; ?>
                                    <?php if (count($technologies) > 3): ?>
                                        <span class="badge bg-light text-muted">+<?php echo count($technologies) - 3; ?></span>
                                    <?php endif; ?>
                                </div>
                            <?php endif; ?>
                            
                            <!-- Client and Date -->
                            <div class="project-meta mt-auto">
                                <?php if ($project['client_name']): ?>
                                    <small class="text-muted d-block">
                                        <i class="fas fa-user me-1"></i>
                                        <?php echo htmlspecialchars($project['client_name']); ?>
                                    </small>
                                <?php endif; ?>
                                <?php if ($project['completion_date']): ?>
                                    <small class="text-muted d-block">
                                        <i class="fas fa-calendar me-1"></i>
                                        <?php echo date('M Y', strtotime($project['completion_date'])); ?>
                                    </small>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Project Modal -->
                <div class="modal fade" id="projectModal<?php echo $project['id']; ?>" tabindex="-1">
                    <div class="modal-dialog modal-lg">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title"><?php echo htmlspecialchars($project['title_hindi']); ?></h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                            </div>
                            <div class="modal-body">
                                <!-- Image Gallery -->
                                <?php if (!empty($images)): ?>
                                    <div id="carousel<?php echo $project['id']; ?>" class="carousel slide mb-4">
                                        <div class="carousel-inner">
                                            <?php foreach ($images as $index => $image): ?>
                                                <div class="carousel-item <?php echo $index === 0 ? 'active' : ''; ?>">
                                                    <img src="<?php echo htmlspecialchars($image); ?>" 
                                                         class="d-block w-100 rounded" 
                                                         alt="Project Image"
                                                         style="height: 300px; object-fit: cover;">
                                                </div>
                                            <?php endforeach; ?>
                                        </div>
                                        <?php if (count($images) > 1): ?>
                                            <button class="carousel-control-prev" type="button" 
                                                    data-bs-target="#carousel<?php echo $project['id']; ?>" 
                                                    data-bs-slide="prev">
                                                <span class="carousel-control-prev-icon"></span>
                                            </button>
                                            <button class="carousel-control-next" type="button" 
                                                    data-bs-target="#carousel<?php echo $project['id']; ?>" 
                                                    data-bs-slide="next">
                                                <span class="carousel-control-next-icon"></span>
                                            </button>
                                        <?php endif; ?>
                                    </div>
                                <?php endif; ?>
                                
                                <!-- Project Details -->
                                <div class="row">
                                    <div class="col-md-8">
                                        <h6>प्रोजेक्ट विवरण:</h6>
                                        <p><?php echo nl2br(htmlspecialchars($project['description_hindi'])); ?></p>
                                        
                                        <?php if (!empty($technologies)): ?>
                                            <h6>उपयोग की गई तकनीकें:</h6>
                                            <div class="mb-3">
                                                <?php foreach ($technologies as $tech): ?>
                                                    <span class="badge bg-primary me-1 mb-1">
                                                        <?php echo htmlspecialchars($tech); ?>
                                                    </span>
                                                <?php endforeach; ?>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                    <div class="col-md-4">
                                        <h6>प्रोजेक्ट जानकारी:</h6>
                                        <ul class="list-unstyled">
                                            <?php if ($project['client_name']): ?>
                                                <li><strong>क्लाइंट:</strong> <?php echo htmlspecialchars($project['client_name']); ?></li>
                                            <?php endif; ?>
                                            <li><strong>प्रकार:</strong> <?php echo $type_label; ?></li>
                                            <?php if ($project['completion_date']): ?>
                                                <li><strong>पूर्ण:</strong> <?php echo date('M Y', strtotime($project['completion_date'])); ?></li>
                                            <?php endif; ?>
                                        </ul>
                                        
                                        <?php if ($project['project_url']): ?>
                                            <a href="<?php echo htmlspecialchars($project['project_url']); ?>" 
                                               target="_blank" 
                                               class="btn btn-primary">
                                                <i class="fas fa-external-link-alt me-2"></i>
                                                लाइव देखें
                                            </a>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>

        <!-- Pagination -->
        <?php if ($total_pages > 1): ?>
            <nav aria-label="Portfolio pagination" class="mt-5">
                <ul class="pagination justify-content-center">
                    <?php if ($page > 1): ?>
                        <li class="page-item">
                            <a class="page-link" href="?page=<?php echo $page - 1; ?><?php echo $project_type ? '&type=' . $project_type : ''; ?>">
                                <i class="fas fa-chevron-left"></i>
                            </a>
                        </li>
                    <?php endif; ?>
                    
                    <?php
                    $start_page = max(1, $page - 2);
                    $end_page = min($total_pages, $page + 2);
                    
                    for ($i = $start_page; $i <= $end_page; $i++):
                    ?>
                        <li class="page-item <?php echo $i == $page ? 'active' : ''; ?>">
                            <a class="page-link" href="?page=<?php echo $i; ?><?php echo $project_type ? '&type=' . $project_type : ''; ?>">
                                <?php echo $i; ?>
                            </a>
                        </li>
                    <?php endfor; ?>
                    
                    <?php if ($page < $total_pages): ?>
                        <li class="page-item">
                            <a class="page-link" href="?page=<?php echo $page + 1; ?><?php echo $project_type ? '&type=' . $project_type : ''; ?>">
                                <i class="fas fa-chevron-right"></i>
                            </a>
                        </li>
                    <?php endif; ?>
                </ul>
            </nav>
        <?php endif; ?>
    <?php endif; ?>

    <!-- Call to Action -->
    <div class="row mt-5">
        <div class="col-12">
            <div class="card bg-gradient-primary text-white border-0 shadow-lg">
                <div class="card-body text-center p-5">
                    <h3 class="mb-3">क्या आप भी ऐसी वेबसाइट चाहते हैं?</h3>
                    <p class="lead mb-4">
                        हमारी विशेषज्ञ टीम आपके लिए भी इसी तरह की प्रोफेशनल वेबसाइट बना सकती है।
                    </p>
                    <div class="cta-buttons">
                        <a href="services.php" class="btn btn-warning btn-lg me-3">
                            <i class="fas fa-eye me-2"></i>
                            सेवाएं देखें
                        </a>
                        <a href="contact.php" class="btn btn-outline-light btn-lg">
                            <i class="fas fa-phone me-2"></i>
                            संपर्क करें
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.portfolio-item {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    position: relative;
    overflow: hidden;
}

.portfolio-item:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0,0,0,0.15);
}

.portfolio-image {
    position: relative;
    overflow: hidden;
}

.portfolio-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0,0,0,0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.portfolio-item:hover .portfolio-overlay {
    opacity: 1;
}

.featured-badge {
    position: absolute;
    top: 15px;
    right: 15px;
    z-index: 2;
}

.technologies .badge {
    font-size: 0.75rem;
}

.nav-pills .nav-link {
    color: #667eea;
    border-radius: 25px;
    margin: 0 5px;
}

.nav-pills .nav-link.active {
    background-color: #667eea;
}

.carousel-control-prev,
.carousel-control-next {
    width: 5%;
}

.carousel-control-prev-icon,
.carousel-control-next-icon {
    background-color: rgba(0,0,0,0.5);
    border-radius: 50%;
    padding: 20px;
}

@media (max-width: 768px) {
    .portfolio-overlay {
        opacity: 1;
        background: rgba(0,0,0,0.3);
    }
    
    .cta-buttons .btn {
        display: block;
        width: 100%;
        margin-bottom: 10px;
    }
}
</style>

<script>
// Add smooth scrolling for filter tabs
document.querySelectorAll('.nav-pills .nav-link').forEach(link => {
    link.addEventListener('click', function(e) {
        // Add loading state
        const currentActive = document.querySelector('.nav-pills .nav-link.active');
        if (currentActive) {
            currentActive.classList.remove('active');
        }
        this.classList.add('active');
    });
});

// Lazy loading for images
if ('IntersectionObserver' in window) {
    const imageObserver = new IntersectionObserver((entries, observer) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const img = entry.target;
                img.src = img.dataset.src;
                img.classList.remove('lazy');
                imageObserver.unobserve(img);
            }
        });
    });

    document.querySelectorAll('img[data-src]').forEach(img => {
        imageObserver.observe(img);
    });
}
</script>

<?php include 'includes/footer.php'; ?>
