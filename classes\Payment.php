<?php
require_once 'config/database.php';

class Payment {
    private $conn;
    private $razorpay_key_id;
    private $razorpay_key_secret;
    
    public function __construct() {
        $this->conn = Config::getDB();
        $this->razorpay_key_id = Config::RAZORPAY_KEY_ID;
        $this->razorpay_key_secret = Config::RAZORPAY_KEY_SECRET;
    }
    
    // Create Razorpay order
    public function createRazorpayOrder($order_id, $amount, $currency = 'INR') {
        try {
            $url = 'https://api.razorpay.com/v1/orders';
            
            $data = [
                'amount' => $amount * 100, // Amount in paise
                'currency' => $currency,
                'receipt' => 'order_' . $order_id,
                'payment_capture' => 1
            ];
            
            $options = [
                'http' => [
                    'header' => [
                        'Content-Type: application/json',
                        'Authorization: Basic ' . base64_encode($this->razorpay_key_id . ':' . $this->razorpay_key_secret)
                    ],
                    'method' => 'POST',
                    'content' => json_encode($data)
                ]
            ];
            
            $context = stream_context_create($options);
            $result = file_get_contents($url, false, $context);
            
            if ($result === FALSE) {
                throw new Exception('Failed to create Razorpay order');
            }
            
            $response = json_decode($result, true);
            
            if (isset($response['error'])) {
                throw new Exception($response['error']['description']);
            }
            
            // Update order with Razorpay order ID
            $this->updateOrderRazorpayId($order_id, $response['id']);
            
            return $response;
            
        } catch (Exception $e) {
            error_log('Razorpay order creation failed: ' . $e->getMessage());
            return false;
        }
    }
    
    // Verify payment signature
    public function verifyPaymentSignature($razorpay_order_id, $razorpay_payment_id, $razorpay_signature) {
        $expected_signature = hash_hmac('sha256', $razorpay_order_id . '|' . $razorpay_payment_id, $this->razorpay_key_secret);
        return hash_equals($expected_signature, $razorpay_signature);
    }
    
    // Process payment success
    public function processPaymentSuccess($order_number, $razorpay_payment_id, $razorpay_order_id, $razorpay_signature) {
        try {
            // Verify signature
            if (!$this->verifyPaymentSignature($razorpay_order_id, $razorpay_payment_id, $razorpay_signature)) {
                throw new Exception('Invalid payment signature');
            }
            
            // Get order details
            $order = new Order();
            if (!$order->getByOrderNumber($order_number)) {
                throw new Exception('Order not found');
            }
            
            // Update payment status
            $order->updatePaymentStatus('paid', $razorpay_payment_id, $razorpay_payment_id);
            
            // Log payment transaction
            $this->logPaymentTransaction($order->id, 'success', $razorpay_payment_id, $razorpay_order_id);
            
            // Send confirmation email
            $this->sendPaymentConfirmationEmail($order);
            
            return true;
            
        } catch (Exception $e) {
            error_log('Payment processing failed: ' . $e->getMessage());
            return false;
        }
    }
    
    // Process payment failure
    public function processPaymentFailure($order_number, $error_description = '') {
        try {
            $order = new Order();
            if ($order->getByOrderNumber($order_number)) {
                $order->updatePaymentStatus('failed');
                $this->logPaymentTransaction($order->id, 'failed', '', '', $error_description);
            }
            return true;
        } catch (Exception $e) {
            error_log('Payment failure processing failed: ' . $e->getMessage());
            return false;
        }
    }
    
    // Update order with Razorpay order ID
    private function updateOrderRazorpayId($order_id, $razorpay_order_id) {
        try {
            $query = "UPDATE orders SET razorpay_order_id = ? WHERE id = ?";
            $stmt = $this->conn->prepare($query);
            return $stmt->execute([$razorpay_order_id, $order_id]);
        } catch (Exception $e) {
            error_log('Failed to update Razorpay order ID: ' . $e->getMessage());
            return false;
        }
    }
    
    // Log payment transaction
    private function logPaymentTransaction($order_id, $status, $payment_id = '', $razorpay_order_id = '', $error_message = '') {
        try {
            $query = "INSERT INTO payment_logs (order_id, status, payment_id, razorpay_order_id, error_message, created_at) 
                     VALUES (?, ?, ?, ?, ?, NOW())";
            $stmt = $this->conn->prepare($query);
            return $stmt->execute([$order_id, $status, $payment_id, $razorpay_order_id, $error_message]);
        } catch (Exception $e) {
            error_log('Payment logging failed: ' . $e->getMessage());
            return false;
        }
    }
    
    // Send payment confirmation email
    private function sendPaymentConfirmationEmail($order) {
        // This will be implemented with the email system
        error_log("Payment confirmation email should be sent for order: " . $order->order_number);
    }
    
    // Get payment details by order
    public function getPaymentByOrder($order_id) {
        try {
            $query = "SELECT * FROM payment_logs WHERE order_id = ? ORDER BY created_at DESC LIMIT 1";
            $stmt = $this->conn->prepare($query);
            $stmt->execute([$order_id]);
            return $stmt->fetch(PDO::FETCH_ASSOC);
        } catch (Exception $e) {
            error_log('Failed to get payment details: ' . $e->getMessage());
            return false;
        }
    }
    
    // Refund payment
    public function refundPayment($payment_id, $amount, $reason = '') {
        try {
            $url = "https://api.razorpay.com/v1/payments/{$payment_id}/refund";
            
            $data = [
                'amount' => $amount * 100, // Amount in paise
                'speed' => 'normal',
                'notes' => [
                    'reason' => $reason
                ]
            ];
            
            $options = [
                'http' => [
                    'header' => [
                        'Content-Type: application/json',
                        'Authorization: Basic ' . base64_encode($this->razorpay_key_id . ':' . $this->razorpay_key_secret)
                    ],
                    'method' => 'POST',
                    'content' => json_encode($data)
                ]
            ];
            
            $context = stream_context_create($options);
            $result = file_get_contents($url, false, $context);
            
            if ($result === FALSE) {
                throw new Exception('Failed to process refund');
            }
            
            $response = json_decode($result, true);
            
            if (isset($response['error'])) {
                throw new Exception($response['error']['description']);
            }
            
            return $response;
            
        } catch (Exception $e) {
            error_log('Refund processing failed: ' . $e->getMessage());
            return false;
        }
    }
    
    // Get payment statistics
    public function getPaymentStats() {
        try {
            $stats = [];
            
            // Total payments
            $query = "SELECT COUNT(*) as total FROM payment_logs WHERE status = 'success'";
            $stmt = $this->conn->prepare($query);
            $stmt->execute();
            $stats['total_payments'] = $stmt->fetch()['total'];
            
            // Failed payments
            $query = "SELECT COUNT(*) as failed FROM payment_logs WHERE status = 'failed'";
            $stmt = $this->conn->prepare($query);
            $stmt->execute();
            $stats['failed_payments'] = $stmt->fetch()['failed'];
            
            // Today's revenue
            $query = "SELECT SUM(o.total_amount) as today_revenue 
                     FROM orders o 
                     JOIN payment_logs pl ON o.id = pl.order_id 
                     WHERE pl.status = 'success' AND DATE(pl.created_at) = CURDATE()";
            $stmt = $this->conn->prepare($query);
            $stmt->execute();
            $stats['today_revenue'] = $stmt->fetch()['today_revenue'] ?? 0;
            
            // This month's revenue
            $query = "SELECT SUM(o.total_amount) as month_revenue 
                     FROM orders o 
                     JOIN payment_logs pl ON o.id = pl.order_id 
                     WHERE pl.status = 'success' 
                     AND MONTH(pl.created_at) = MONTH(CURRENT_DATE()) 
                     AND YEAR(pl.created_at) = YEAR(CURRENT_DATE())";
            $stmt = $this->conn->prepare($query);
            $stmt->execute();
            $stats['month_revenue'] = $stmt->fetch()['month_revenue'] ?? 0;
            
            return $stats;
            
        } catch (Exception $e) {
            error_log('Failed to get payment stats: ' . $e->getMessage());
            return [
                'total_payments' => 0,
                'failed_payments' => 0,
                'today_revenue' => 0,
                'month_revenue' => 0
            ];
        }
    }
    
    // Validate webhook signature
    public function validateWebhookSignature($payload, $signature) {
        $expected_signature = hash_hmac('sha256', $payload, $this->razorpay_key_secret);
        return hash_equals($expected_signature, $signature);
    }
    
    // Process webhook
    public function processWebhook($payload, $signature) {
        try {
            if (!$this->validateWebhookSignature($payload, $signature)) {
                throw new Exception('Invalid webhook signature');
            }
            
            $data = json_decode($payload, true);
            
            if (!$data) {
                throw new Exception('Invalid webhook payload');
            }
            
            $event = $data['event'];
            $payment_entity = $data['payload']['payment']['entity'];
            
            switch ($event) {
                case 'payment.captured':
                    $this->handlePaymentCaptured($payment_entity);
                    break;
                    
                case 'payment.failed':
                    $this->handlePaymentFailed($payment_entity);
                    break;
                    
                case 'refund.created':
                    $this->handleRefundCreated($data['payload']['refund']['entity']);
                    break;
                    
                default:
                    error_log('Unhandled webhook event: ' . $event);
            }
            
            return true;
            
        } catch (Exception $e) {
            error_log('Webhook processing failed: ' . $e->getMessage());
            return false;
        }
    }
    
    // Handle payment captured webhook
    private function handlePaymentCaptured($payment_entity) {
        $order_receipt = $payment_entity['order_id'];
        // Extract order ID from receipt
        if (preg_match('/order_(\d+)/', $order_receipt, $matches)) {
            $order_id = $matches[1];
            $order = new Order();
            if ($order->getById($order_id)) {
                $order->updatePaymentStatus('paid', $payment_entity['id'], $payment_entity['id']);
                $this->logPaymentTransaction($order_id, 'webhook_captured', $payment_entity['id'], $order_receipt);
            }
        }
    }
    
    // Handle payment failed webhook
    private function handlePaymentFailed($payment_entity) {
        $order_receipt = $payment_entity['order_id'];
        if (preg_match('/order_(\d+)/', $order_receipt, $matches)) {
            $order_id = $matches[1];
            $order = new Order();
            if ($order->getById($order_id)) {
                $order->updatePaymentStatus('failed');
                $this->logPaymentTransaction($order_id, 'webhook_failed', '', $order_receipt, $payment_entity['error_description'] ?? '');
            }
        }
    }
    
    // Handle refund created webhook
    private function handleRefundCreated($refund_entity) {
        $payment_id = $refund_entity['payment_id'];
        // Update order status to refunded
        $query = "UPDATE orders o 
                 JOIN payment_logs pl ON o.id = pl.order_id 
                 SET o.payment_status = 'refunded' 
                 WHERE pl.payment_id = ?";
        $stmt = $this->conn->prepare($query);
        $stmt->execute([$payment_id]);
    }
}

// Add payment_logs table to schema if not exists
try {
    $db = Config::getDB();
    $db->exec("
        CREATE TABLE IF NOT EXISTS payment_logs (
            id INT AUTO_INCREMENT PRIMARY KEY,
            order_id INT NOT NULL,
            status ENUM('success', 'failed', 'webhook_captured', 'webhook_failed') NOT NULL,
            payment_id VARCHAR(100),
            razorpay_order_id VARCHAR(100),
            error_message TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (order_id) REFERENCES orders(id) ON DELETE CASCADE
        )
    ");
} catch(Exception $e) {
    error_log('Failed to create payment_logs table: ' . $e->getMessage());
}
?>
