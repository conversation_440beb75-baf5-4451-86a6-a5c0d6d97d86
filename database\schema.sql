-- Web Development Services Database Schema
-- Created for Indian Web Development Services Business

CREATE DATABASE IF NOT EXISTS webdev_services CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE webdev_services;

-- Users table for authentication and role management
CREATE TABLE users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    full_name VARCHAR(100) NOT NULL,
    phone VARCHAR(15),
    role ENUM('admin', 'client') DEFAULT 'client',
    status ENUM('active', 'inactive', 'suspended') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Services table for the three packages
CREATE TABLE services (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    name_hindi VARCHAR(100) NOT NULL,
    description TEXT,
    description_hindi TEXT,
    price DECIMAL(10,2) NOT NULL,
    features JSON,
    features_hindi JSON,
    package_type ENUM('business', 'ecommerce', 'custom') NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Orders table for tracking client orders
CREATE TABLE orders (
    id INT AUTO_INCREMENT PRIMARY KEY,
    order_number VARCHAR(20) UNIQUE NOT NULL,
    user_id INT NOT NULL,
    service_id INT NOT NULL,
    status ENUM('pending', 'confirmed', 'in_progress', 'completed', 'cancelled') DEFAULT 'pending',
    total_amount DECIMAL(10,2) NOT NULL,
    payment_status ENUM('pending', 'paid', 'failed', 'refunded') DEFAULT 'pending',
    payment_id VARCHAR(100),
    razorpay_order_id VARCHAR(100),
    razorpay_payment_id VARCHAR(100),
    client_requirements TEXT,
    admin_notes TEXT,
    estimated_delivery DATE,
    actual_delivery DATE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (service_id) REFERENCES services(id) ON DELETE RESTRICT
);

-- Order status history for tracking progress
CREATE TABLE order_status_history (
    id INT AUTO_INCREMENT PRIMARY KEY,
    order_id INT NOT NULL,
    status ENUM('pending', 'confirmed', 'in_progress', 'completed', 'cancelled') NOT NULL,
    notes TEXT,
    changed_by INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (order_id) REFERENCES orders(id) ON DELETE CASCADE,
    FOREIGN KEY (changed_by) REFERENCES users(id) ON DELETE CASCADE
);

-- Blog categories
CREATE TABLE blog_categories (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    name_hindi VARCHAR(100) NOT NULL,
    slug VARCHAR(100) UNIQUE NOT NULL,
    description TEXT,
    description_hindi TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Blog posts
CREATE TABLE blog_posts (
    id INT AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(200) NOT NULL,
    title_hindi VARCHAR(200) NOT NULL,
    slug VARCHAR(200) UNIQUE NOT NULL,
    content LONGTEXT NOT NULL,
    content_hindi LONGTEXT NOT NULL,
    excerpt TEXT,
    excerpt_hindi TEXT,
    featured_image VARCHAR(255),
    category_id INT,
    author_id INT NOT NULL,
    status ENUM('draft', 'published', 'archived') DEFAULT 'draft',
    meta_title VARCHAR(200),
    meta_description TEXT,
    tags JSON,
    view_count INT DEFAULT 0,
    published_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (category_id) REFERENCES blog_categories(id) ON DELETE SET NULL,
    FOREIGN KEY (author_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Portfolio projects
CREATE TABLE portfolio (
    id INT AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(200) NOT NULL,
    title_hindi VARCHAR(200) NOT NULL,
    description TEXT,
    description_hindi TEXT,
    client_name VARCHAR(100),
    project_url VARCHAR(255),
    image_gallery JSON,
    technologies_used JSON,
    project_type ENUM('business', 'ecommerce', 'custom') NOT NULL,
    completion_date DATE,
    is_featured BOOLEAN DEFAULT FALSE,
    display_order INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Contact inquiries
CREATE TABLE contact_inquiries (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    email VARCHAR(100) NOT NULL,
    phone VARCHAR(15),
    subject VARCHAR(200),
    message TEXT NOT NULL,
    service_interest ENUM('business', 'ecommerce', 'custom', 'general'),
    status ENUM('new', 'replied', 'closed') DEFAULT 'new',
    admin_reply TEXT,
    replied_at TIMESTAMP NULL,
    replied_by INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (replied_by) REFERENCES users(id) ON DELETE SET NULL
);

-- Email templates
CREATE TABLE email_templates (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    subject VARCHAR(200) NOT NULL,
    subject_hindi VARCHAR(200) NOT NULL,
    body LONGTEXT NOT NULL,
    body_hindi LONGTEXT NOT NULL,
    template_type ENUM('order_confirmation', 'status_update', 'newsletter', 'contact_reply') NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Newsletter subscribers
CREATE TABLE newsletter_subscribers (
    id INT AUTO_INCREMENT PRIMARY KEY,
    email VARCHAR(100) UNIQUE NOT NULL,
    name VARCHAR(100),
    status ENUM('active', 'unsubscribed') DEFAULT 'active',
    subscribed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    unsubscribed_at TIMESTAMP NULL
);

-- Email logs for tracking sent emails
CREATE TABLE email_logs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    recipient_email VARCHAR(100) NOT NULL,
    subject VARCHAR(200) NOT NULL,
    template_id INT,
    order_id INT,
    status ENUM('sent', 'failed') NOT NULL,
    error_message TEXT,
    sent_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (template_id) REFERENCES email_templates(id) ON DELETE SET NULL,
    FOREIGN KEY (order_id) REFERENCES orders(id) ON DELETE SET NULL
);

-- Site settings
CREATE TABLE site_settings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    setting_key VARCHAR(100) UNIQUE NOT NULL,
    setting_value TEXT,
    setting_type ENUM('text', 'number', 'boolean', 'json') DEFAULT 'text',
    description VARCHAR(255),
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Insert default admin user
INSERT INTO users (username, email, password, full_name, role) VALUES 
('admin', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Admin User', 'admin');

-- Insert default services
INSERT INTO services (name, name_hindi, description, description_hindi, price, features, features_hindi, package_type) VALUES 
('Business Premium Package', 'बिजनेस प्रीमियम पैकेज', 'Complete business website with authentication and email system', 'प्रमाणीकरण और ईमेल सिस्टम के साथ पूर्ण व्यावसायिक वेबसाइट', 15000.00, 
'["Free Domain & Hosting Setup", "Multi-page website with secure authentication", "Query Dashboard", "Automatic Email Sender", "reCAPTCHA integration", "On-page SEO optimization", "Google Analytics integration", "WhatsApp/Phone/Email support links"]',
'["मुफ्त डोमेन और होस्टिंग सेटअप", "सुरक्षित प्रमाणीकरण के साथ मल्टी-पेज वेबसाइट", "क्वेरी डैशबोर्ड", "स्वचालित ईमेल भेजने वाला", "reCAPTCHA एकीकरण", "ऑन-पेज SEO अनुकूलन", "Google Analytics एकीकरण", "WhatsApp/फोन/ईमेल सहायता लिंक"]',
'business'),

('E-Commerce Package', 'ई-कॉमर्स पैकेज', 'Complete e-commerce solution with multiple dashboards and payment gateway', 'कई डैशबोर्ड और पेमेंट गेटवे के साथ पूर्ण ई-कॉमर्स समाधान', 75000.00,
'["Free Domain & Hosting Setup", "Multi-page storefront", "Three separate dashboards", "Role-based access control", "Payment Gateway Integration (Razorpay)", "Automatic Email System", "reCAPTCHA on critical forms", "On-page SEO + Google Analytics"]',
'["मुफ्त डोमेन और होस्टिंग सेटअप", "मल्टी-पेज स्टोरफ्रंट", "तीन अलग डैशबोर्ड", "भूमिका-आधारित पहुंच नियंत्रण", "पेमेंट गेटवे एकीकरण (Razorpay)", "स्वचालित ईमेल सिस्टम", "महत्वपूर्ण फॉर्म पर reCAPTCHA", "ऑन-पेज SEO + Google Analytics"]',
'ecommerce'),

('Custom Development', 'कस्टम डेवलपमेंट', 'Custom solutions based on specific requirements', 'विशिष्ट आवश्यकताओं के आधार पर कस्टम समाधान', 0.00,
'["Custom solutions: CRM, booking systems, LMS, portals", "Modular pricing", "Google Analytics integration", "Essential on-page SEO", "Requirement-based scoping"]',
'["कस्टम समाधान: CRM, बुकिंग सिस्टम, LMS, पोर्टल", "मॉड्यूलर मूल्य निर्धारण", "Google Analytics एकीकरण", "आवश्यक ऑन-पेज SEO", "आवश्यकता-आधारित स्कोपिंग"]',
'custom');

-- Insert default email templates
INSERT INTO email_templates (name, subject, subject_hindi, body, body_hindi, template_type) VALUES 
('Order Confirmation', 'Order Confirmation - {{order_number}}', 'ऑर्डर पुष्टि - {{order_number}}', 
'Dear {{customer_name}},\n\nThank you for your order! Your order {{order_number}} has been confirmed.\n\nOrder Details:\nService: {{service_name}}\nAmount: ₹{{amount}}\n\nWe will start working on your project soon.\n\nBest regards,\nWeb Development Services Team',
'प्रिय {{customer_name}},\n\nआपके ऑर्डर के लिए धन्यवाद! आपका ऑर्डर {{order_number}} पुष्ट हो गया है।\n\nऑर्डर विवरण:\nसेवा: {{service_name}}\nराशि: ₹{{amount}}\n\nहम जल्द ही आपके प्रोजेक्ट पर काम शुरू करेंगे।\n\nसादर,\nवेब डेवलपमेंट सर्विसेज टीम',
'order_confirmation');

-- Insert default site settings
INSERT INTO site_settings (setting_key, setting_value, setting_type, description) VALUES 
('site_name', 'Web Development Services', 'text', 'Website name'),
('site_name_hindi', 'वेब डेवलपमेंट सर्विसेज', 'text', 'Website name in Hindi'),
('contact_email', '<EMAIL>', 'text', 'Contact email'),
('contact_phone', '+91-9876543210', 'text', 'Contact phone'),
('whatsapp_number', '+91-9876543210', 'text', 'WhatsApp number'),
('razorpay_key_id', '', 'text', 'Razorpay Key ID'),
('razorpay_key_secret', '', 'text', 'Razorpay Key Secret'),
('google_analytics_id', '', 'text', 'Google Analytics Tracking ID'),
('recaptcha_site_key', '', 'text', 'Google reCAPTCHA Site Key'),
('recaptcha_secret_key', '', 'text', 'Google reCAPTCHA Secret Key');
