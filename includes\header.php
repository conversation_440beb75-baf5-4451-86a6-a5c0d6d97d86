<!DOCTYPE html>
<html lang="hi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="<?php echo isset($meta_description) ? $meta_description : 'भारतीय बाजार के लिए पेशेवर वेब डेवलपमेंट सेवाएं - HTML, CSS, JS, PHP और MySQL आधारित वेबसाइट समाधान'; ?>">
    <meta name="keywords" content="वेब डेवलपमेंट, वेबसाइट डिजाइन, ई-कॉमर्स, PHP, MySQL, भारत">
    <meta name="author" content="Web Development Services">
    
    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="<?php echo isset($page_title) ? $page_title : Config::SITE_NAME_HINDI; ?>">
    <meta property="og:description" content="<?php echo isset($meta_description) ? $meta_description : 'भारतीय बाजार के लिए पेशेवर वेब डेवलपमेंट सेवाएं'; ?>">
    <meta property="og:type" content="website">
    <meta property="og:url" content="<?php echo Config::SITE_URL . $_SERVER['REQUEST_URI']; ?>">
    <meta property="og:image" content="<?php echo Config::SITE_URL; ?>/assets/images/og-image.jpg">
    
    <title><?php echo isset($page_title) ? $page_title . ' - ' . Config::SITE_NAME_HINDI : Config::SITE_NAME_HINDI; ?></title>
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="<?php echo Config::SITE_URL; ?>/assets/images/favicon.ico">
    
    <!-- CSS Files -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+Devanagari:wght@300;400;500;600;700&family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="<?php echo Config::SITE_URL; ?>/assets/css/style.css" rel="stylesheet">
    
    <!-- Google Analytics -->
    <?php if (Config::GOOGLE_ANALYTICS_ID): ?>
    <script async src="https://www.googletagmanager.com/gtag/js?id=<?php echo Config::GOOGLE_ANALYTICS_ID; ?>"></script>
    <script>
        window.dataLayer = window.dataLayer || [];
        function gtag(){dataLayer.push(arguments);}
        gtag('js', new Date());
        gtag('config', '<?php echo Config::GOOGLE_ANALYTICS_ID; ?>');
    </script>
    <?php endif; ?>
    
    <!-- Google reCAPTCHA -->
    <?php if (Config::RECAPTCHA_SITE_KEY): ?>
    <script src="https://www.google.com/recaptcha/api.js" async defer></script>
    <?php endif; ?>
    
    <!-- Razorpay Checkout -->
    <script src="https://checkout.razorpay.com/v1/checkout.js"></script>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary fixed-top">
        <div class="container">
            <a class="navbar-brand fw-bold" href="<?php echo Config::SITE_URL; ?>">
                <i class="fas fa-code me-2"></i>
                <?php echo Config::SITE_NAME_HINDI; ?>
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'index.php' ? 'active' : ''; ?>" href="<?php echo Config::SITE_URL; ?>">
                            <i class="fas fa-home me-1"></i>होम
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'services.php' ? 'active' : ''; ?>" href="<?php echo Config::SITE_URL; ?>/services.php">
                            <i class="fas fa-cogs me-1"></i>सेवाएं
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'portfolio.php' ? 'active' : ''; ?>" href="<?php echo Config::SITE_URL; ?>/portfolio.php">
                            <i class="fas fa-briefcase me-1"></i>पोर्टफोलियो
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'blog.php' ? 'active' : ''; ?>" href="<?php echo Config::SITE_URL; ?>/blog.php">
                            <i class="fas fa-blog me-1"></i>ब्लॉग
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'contact.php' ? 'active' : ''; ?>" href="<?php echo Config::SITE_URL; ?>/contact.php">
                            <i class="fas fa-envelope me-1"></i>संपर्क
                        </a>
                    </li>
                </ul>
                
                <ul class="navbar-nav">
                    <?php if (isset($_SESSION['user_id'])): ?>
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                                <i class="fas fa-user me-1"></i>
                                <?php echo htmlspecialchars($_SESSION['full_name']); ?>
                            </a>
                            <ul class="dropdown-menu">
                                <?php if ($_SESSION['role'] == 'admin'): ?>
                                    <li><a class="dropdown-item" href="<?php echo Config::SITE_URL; ?>/admin/dashboard.php">
                                        <i class="fas fa-tachometer-alt me-2"></i>एडमिन डैशबोर्ड
                                    </a></li>
                                <?php else: ?>
                                    <li><a class="dropdown-item" href="<?php echo Config::SITE_URL; ?>/client/dashboard.php">
                                        <i class="fas fa-tachometer-alt me-2"></i>डैशबोर्ड
                                    </a></li>
                                <?php endif; ?>
                                <li><a class="dropdown-item" href="<?php echo Config::SITE_URL; ?>/client/orders.php">
                                    <i class="fas fa-shopping-cart me-2"></i>मेरे ऑर्डर
                                </a></li>
                                <li><a class="dropdown-item" href="<?php echo Config::SITE_URL; ?>/client/profile.php">
                                    <i class="fas fa-user-edit me-2"></i>प्रोफाइल
                                </a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="<?php echo Config::SITE_URL; ?>/auth/logout.php">
                                    <i class="fas fa-sign-out-alt me-2"></i>लॉगआउट
                                </a></li>
                            </ul>
                        </li>
                    <?php else: ?>
                        <li class="nav-item">
                            <a class="nav-link" href="<?php echo Config::SITE_URL; ?>/auth/login.php">
                                <i class="fas fa-sign-in-alt me-1"></i>लॉगिन
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="<?php echo Config::SITE_URL; ?>/auth/register.php">
                                <i class="fas fa-user-plus me-1"></i>रजिस्टर
                            </a>
                        </li>
                    <?php endif; ?>
                </ul>
            </div>
        </div>
    </nav>
    
    <!-- Main Content -->
    <main class="main-content">
        <?php if (isset($_SESSION['success_message'])): ?>
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                <i class="fas fa-check-circle me-2"></i>
                <?php echo $_SESSION['success_message']; unset($_SESSION['success_message']); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>
        
        <?php if (isset($_SESSION['error_message'])): ?>
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <i class="fas fa-exclamation-circle me-2"></i>
                <?php echo $_SESSION['error_message']; unset($_SESSION['error_message']); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>
        
        <?php if (isset($_SESSION['info_message'])): ?>
            <div class="alert alert-info alert-dismissible fade show" role="alert">
                <i class="fas fa-info-circle me-2"></i>
                <?php echo $_SESSION['info_message']; unset($_SESSION['info_message']); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>
