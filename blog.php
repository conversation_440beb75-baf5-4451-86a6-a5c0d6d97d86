<?php
require_once 'config/database.php';
require_once 'classes/User.php';
require_once 'classes/Blog.php';

$page_title = 'ब्लॉग - वेब डेवलपमेंट टिप्स और ट्रिक्स';
$meta_description = 'वेब डेवलपमेंट, डिजाइन और डिजिटल मार्केटिंग के बारे में नवीनतम जानकारी और टिप्स पढ़ें।';

// Get parameters
$page = max(1, intval($_GET['page'] ?? 1));
$category_id = intval($_GET['category'] ?? 0);
$search = sanitizeInput($_GET['search'] ?? '');
$posts_per_page = Config::POSTS_PER_PAGE;
$offset = ($page - 1) * $posts_per_page;

// Initialize blog
$blog = new Blog();

// Get posts
$posts = $blog->getAllPublished($posts_per_page, $offset, $category_id ?: null, $search);

// Get total posts count for pagination
try {
    $db = Config::getDB();
    $count_query = "SELECT COUNT(*) as total FROM blog_posts WHERE status = 'published'";
    $count_params = [];
    
    if ($category_id) {
        $count_query .= " AND category_id = ?";
        $count_params[] = $category_id;
    }
    
    if (!empty($search)) {
        $count_query .= " AND (title_hindi LIKE ? OR content_hindi LIKE ? OR tags LIKE ?)";
        $search_term = '%' . $search . '%';
        $count_params[] = $search_term;
        $count_params[] = $search_term;
        $count_params[] = $search_term;
    }
    
    $count_stmt = $db->prepare($count_query);
    $count_stmt->execute($count_params);
    $total_posts = $count_stmt->fetch()['total'];
    $total_pages = ceil($total_posts / $posts_per_page);
} catch(Exception $e) {
    $total_posts = 0;
    $total_pages = 1;
}

// Get categories
$categories = $blog->getCategories();

// Get popular posts
$popular_posts = $blog->getPopularPosts(5);

// Get recent posts
$recent_posts = $blog->getRecentPosts(5);

include 'includes/header.php';
?>

<div class="container mt-5 pt-5">
    <!-- Page Header -->
    <div class="row mb-5">
        <div class="col-12">
            <div class="text-center">
                <h1 class="display-4 fw-bold">ब्लॉग</h1>
                <p class="lead text-muted">वेब डेवलपमेंट की दुनिया से नवीनतम जानकारी और टिप्स</p>
            </div>
        </div>
    </div>

    <!-- Search and Filter -->
    <div class="row mb-4">
        <div class="col-lg-8">
            <form method="GET" action="" class="d-flex gap-2">
                <input type="text" name="search" class="form-control" 
                       placeholder="ब्लॉग पोस्ट खोजें..." 
                       value="<?php echo htmlspecialchars($search); ?>">
                <select name="category" class="form-select" style="max-width: 200px;">
                    <option value="">सभी कैटेगरी</option>
                    <?php foreach ($categories as $category): ?>
                        <option value="<?php echo $category['id']; ?>" 
                                <?php echo $category_id == $category['id'] ? 'selected' : ''; ?>>
                            <?php echo htmlspecialchars($category['name_hindi']); ?>
                        </option>
                    <?php endforeach; ?>
                </select>
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-search"></i>
                </button>
            </form>
        </div>
        <div class="col-lg-4 text-lg-end">
            <?php if (!empty($search) || $category_id): ?>
                <a href="blog.php" class="btn btn-outline-secondary">
                    <i class="fas fa-times me-2"></i>
                    फिल्टर हटाएं
                </a>
            <?php endif; ?>
        </div>
    </div>

    <div class="row">
        <!-- Main Content -->
        <div class="col-lg-8">
            <?php if (empty($posts)): ?>
                <div class="text-center py-5">
                    <i class="fas fa-search fa-3x text-muted mb-3"></i>
                    <h4 class="text-muted">कोई पोस्ट नहीं मिली</h4>
                    <p class="text-muted">
                        <?php if (!empty($search)): ?>
                            "<?php echo htmlspecialchars($search); ?>" के लिए कोई परिणाम नहीं मिला।
                        <?php else: ?>
                            अभी तक कोई ब्लॉग पोस्ट प्रकाशित नहीं की गई है।
                        <?php endif; ?>
                    </p>
                </div>
            <?php else: ?>
                <!-- Blog Posts -->
                <div class="row g-4">
                    <?php foreach ($posts as $post): ?>
                        <div class="col-md-6 mb-4">
                            <article class="card h-100 shadow-sm border-0 blog-card">
                                <?php if ($post['featured_image']): ?>
                                    <div class="blog-image">
                                        <img src="<?php echo htmlspecialchars($post['featured_image']); ?>" 
                                             class="card-img-top" 
                                             alt="<?php echo htmlspecialchars($post['title_hindi']); ?>"
                                             style="height: 200px; object-fit: cover;">
                                    </div>
                                <?php endif; ?>
                                
                                <div class="card-body d-flex flex-column">
                                    <!-- Category Badge -->
                                    <?php if ($post['category_name_hindi']): ?>
                                        <div class="mb-2">
                                            <span class="badge bg-primary">
                                                <?php echo htmlspecialchars($post['category_name_hindi']); ?>
                                            </span>
                                        </div>
                                    <?php endif; ?>
                                    
                                    <!-- Title -->
                                    <h5 class="card-title">
                                        <a href="blog-post.php?slug=<?php echo htmlspecialchars($post['slug']); ?>" 
                                           class="text-decoration-none text-dark">
                                            <?php echo htmlspecialchars($post['title_hindi']); ?>
                                        </a>
                                    </h5>
                                    
                                    <!-- Excerpt -->
                                    <p class="card-text text-muted flex-grow-1">
                                        <?php echo htmlspecialchars($post['excerpt_hindi']); ?>
                                    </p>
                                    
                                    <!-- Meta Info -->
                                    <div class="blog-meta d-flex justify-content-between align-items-center mt-auto">
                                        <small class="text-muted">
                                            <i class="fas fa-user me-1"></i>
                                            <?php echo htmlspecialchars($post['author_name']); ?>
                                        </small>
                                        <small class="text-muted">
                                            <i class="fas fa-calendar me-1"></i>
                                            <?php echo date('d/m/Y', strtotime($post['published_at'])); ?>
                                        </small>
                                    </div>
                                    
                                    <!-- Read More -->
                                    <div class="mt-3">
                                        <a href="blog-post.php?slug=<?php echo htmlspecialchars($post['slug']); ?>" 
                                           class="btn btn-outline-primary btn-sm">
                                            <i class="fas fa-arrow-right me-2"></i>
                                            पूरा पढ़ें
                                        </a>
                                        
                                        <!-- View Count -->
                                        <span class="float-end text-muted small">
                                            <i class="fas fa-eye me-1"></i>
                                            <?php echo number_format($post['view_count']); ?>
                                        </span>
                                    </div>
                                </div>
                            </article>
                        </div>
                    <?php endforeach; ?>
                </div>

                <!-- Pagination -->
                <?php if ($total_pages > 1): ?>
                    <nav aria-label="Blog pagination" class="mt-5">
                        <ul class="pagination justify-content-center">
                            <!-- Previous Page -->
                            <?php if ($page > 1): ?>
                                <li class="page-item">
                                    <a class="page-link" href="?page=<?php echo $page - 1; ?><?php echo $category_id ? '&category=' . $category_id : ''; ?><?php echo $search ? '&search=' . urlencode($search) : ''; ?>">
                                        <i class="fas fa-chevron-left"></i>
                                    </a>
                                </li>
                            <?php endif; ?>
                            
                            <!-- Page Numbers -->
                            <?php
                            $start_page = max(1, $page - 2);
                            $end_page = min($total_pages, $page + 2);
                            
                            for ($i = $start_page; $i <= $end_page; $i++):
                            ?>
                                <li class="page-item <?php echo $i == $page ? 'active' : ''; ?>">
                                    <a class="page-link" href="?page=<?php echo $i; ?><?php echo $category_id ? '&category=' . $category_id : ''; ?><?php echo $search ? '&search=' . urlencode($search) : ''; ?>">
                                        <?php echo $i; ?>
                                    </a>
                                </li>
                            <?php endfor; ?>
                            
                            <!-- Next Page -->
                            <?php if ($page < $total_pages): ?>
                                <li class="page-item">
                                    <a class="page-link" href="?page=<?php echo $page + 1; ?><?php echo $category_id ? '&category=' . $category_id : ''; ?><?php echo $search ? '&search=' . urlencode($search) : ''; ?>">
                                        <i class="fas fa-chevron-right"></i>
                                    </a>
                                </li>
                            <?php endif; ?>
                        </ul>
                    </nav>
                <?php endif; ?>
            <?php endif; ?>
        </div>

        <!-- Sidebar -->
        <div class="col-lg-4">
            <!-- Search Widget -->
            <div class="card shadow-sm border-0 mb-4">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-search me-2"></i>
                        खोजें
                    </h5>
                </div>
                <div class="card-body">
                    <form method="GET" action="">
                        <div class="input-group">
                            <input type="text" name="search" class="form-control" 
                                   placeholder="खोजें..." 
                                   value="<?php echo htmlspecialchars($search); ?>">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-search"></i>
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Categories Widget -->
            <div class="card shadow-sm border-0 mb-4">
                <div class="card-header bg-success text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-folder me-2"></i>
                        कैटेगरी
                    </h5>
                </div>
                <div class="card-body">
                    <ul class="list-unstyled mb-0">
                        <li class="mb-2">
                            <a href="blog.php" class="text-decoration-none <?php echo !$category_id ? 'fw-bold text-primary' : 'text-muted'; ?>">
                                सभी पोस्ट
                            </a>
                        </li>
                        <?php foreach ($categories as $category): ?>
                            <li class="mb-2">
                                <a href="blog.php?category=<?php echo $category['id']; ?>" 
                                   class="text-decoration-none <?php echo $category_id == $category['id'] ? 'fw-bold text-primary' : 'text-muted'; ?>">
                                    <?php echo htmlspecialchars($category['name_hindi']); ?>
                                </a>
                            </li>
                        <?php endforeach; ?>
                    </ul>
                </div>
            </div>

            <!-- Popular Posts Widget -->
            <?php if (!empty($popular_posts)): ?>
                <div class="card shadow-sm border-0 mb-4">
                    <div class="card-header bg-warning text-dark">
                        <h5 class="mb-0">
                            <i class="fas fa-fire me-2"></i>
                            लोकप्रिय पोस्ट
                        </h5>
                    </div>
                    <div class="card-body">
                        <?php foreach ($popular_posts as $popular_post): ?>
                            <div class="d-flex mb-3">
                                <?php if ($popular_post['featured_image']): ?>
                                    <img src="<?php echo htmlspecialchars($popular_post['featured_image']); ?>" 
                                         class="me-3 rounded" 
                                         style="width: 60px; height: 60px; object-fit: cover;"
                                         alt="<?php echo htmlspecialchars($popular_post['title_hindi']); ?>">
                                <?php endif; ?>
                                <div class="flex-grow-1">
                                    <h6 class="mb-1">
                                        <a href="blog-post.php?slug=<?php echo htmlspecialchars($popular_post['slug']); ?>" 
                                           class="text-decoration-none text-dark">
                                            <?php echo htmlspecialchars(substr($popular_post['title_hindi'], 0, 50)) . (strlen($popular_post['title_hindi']) > 50 ? '...' : ''); ?>
                                        </a>
                                    </h6>
                                    <small class="text-muted">
                                        <i class="fas fa-eye me-1"></i>
                                        <?php echo number_format($popular_post['view_count']); ?> views
                                    </small>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            <?php endif; ?>

            <!-- Recent Posts Widget -->
            <?php if (!empty($recent_posts)): ?>
                <div class="card shadow-sm border-0 mb-4">
                    <div class="card-header bg-info text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-clock me-2"></i>
                            हाल की पोस्ट
                        </h5>
                    </div>
                    <div class="card-body">
                        <?php foreach ($recent_posts as $recent_post): ?>
                            <div class="d-flex mb-3">
                                <?php if ($recent_post['featured_image']): ?>
                                    <img src="<?php echo htmlspecialchars($recent_post['featured_image']); ?>" 
                                         class="me-3 rounded" 
                                         style="width: 60px; height: 60px; object-fit: cover;"
                                         alt="<?php echo htmlspecialchars($recent_post['title_hindi']); ?>">
                                <?php endif; ?>
                                <div class="flex-grow-1">
                                    <h6 class="mb-1">
                                        <a href="blog-post.php?slug=<?php echo htmlspecialchars($recent_post['slug']); ?>" 
                                           class="text-decoration-none text-dark">
                                            <?php echo htmlspecialchars(substr($recent_post['title_hindi'], 0, 50)) . (strlen($recent_post['title_hindi']) > 50 ? '...' : ''); ?>
                                        </a>
                                    </h6>
                                    <small class="text-muted">
                                        <?php echo date('d/m/Y', strtotime($recent_post['published_at'])); ?>
                                    </small>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            <?php endif; ?>

            <!-- Newsletter Subscription -->
            <div class="card shadow-sm border-0 mb-4">
                <div class="card-header bg-dark text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-envelope me-2"></i>
                        न्यूज़लेटर
                    </h5>
                </div>
                <div class="card-body">
                    <p class="text-muted">नवीनतम पोस्ट और अपडेट के लिए सब्सक्राइब करें।</p>
                    <form class="newsletter-form">
                        <div class="input-group">
                            <input type="email" name="email" class="form-control" 
                                   placeholder="आपका ईमेल" required>
                            <button type="submit" class="btn btn-dark">
                                <i class="fas fa-paper-plane"></i>
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.blog-card {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.blog-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.blog-image {
    overflow: hidden;
}

.blog-image img {
    transition: transform 0.3s ease;
}

.blog-card:hover .blog-image img {
    transform: scale(1.05);
}

.page-link {
    color: #667eea;
    border-color: #dee2e6;
}

.page-link:hover {
    color: #5a6fd8;
    background-color: #f8f9fa;
    border-color: #dee2e6;
}

.page-item.active .page-link {
    background-color: #667eea;
    border-color: #667eea;
}

@media (max-width: 768px) {
    .blog-meta {
        flex-direction: column;
        align-items: flex-start !important;
    }
    
    .blog-meta small {
        margin-bottom: 5px;
    }
}
</style>

<script>
// Newsletter subscription
document.querySelector('.newsletter-form').addEventListener('submit', function(e) {
    e.preventDefault();
    const formData = new FormData(this);
    
    fetch('includes/newsletter.php', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('न्यूज़लेटर सब्सक्रिप्शन सफल! धन्यवाद।');
            this.reset();
        } else {
            alert('त्रुटि: ' + data.message);
        }
    })
    .catch(error => {
        alert('कुछ गलत हुआ। कृपया बाद में पुनः प्रयास करें।');
    });
});
</script>

<?php include 'includes/footer.php'; ?>
