<?php
require_once 'config/database.php';
require_once 'classes/User.php';
require_once 'includes/functions.php';

$page_title = 'संपर्क करें - वेब डेवलपमेंट सर्विसेज';
$meta_description = 'हमसे संपर्क करें। वेब डेवलपमेंट, ई-कॉमर्स और कस्टम सॉफ्टवेयर डेवलपमेंट के लिए मुफ्त सलाह लें।';

$error_messages = [];
$success_message = '';

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    // Verify CSRF token
    if (!isset($_POST['csrf_token']) || !verifyCsrfToken($_POST['csrf_token'])) {
        $error_messages[] = 'सुरक्षा त्रुटि। कृपया पुनः प्रयास करें।';
    } else {
        $name = sanitizeInput($_POST['name']);
        $email = sanitizeInput($_POST['email']);
        $phone = sanitizeInput($_POST['phone']);
        $subject = sanitizeInput($_POST['subject']);
        $message = sanitizeInput($_POST['message']);
        $service_interest = sanitizeInput($_POST['service_interest'] ?? '');
        
        // Validation
        if (empty($name)) {
            $error_messages[] = 'कृपया अपना नाम दर्ज करें।';
        }
        
        if (empty($email) || !validateEmail($email)) {
            $error_messages[] = 'कृपया वैध ईमेल पता दर्ज करें।';
        }
        
        if (!empty($phone) && !validatePhone($phone)) {
            $error_messages[] = 'कृपया वैध फोन नंबर दर्ज करें।';
        }
        
        if (empty($subject)) {
            $error_messages[] = 'कृपया विषय दर्ज करें।';
        }
        
        if (empty($message)) {
            $error_messages[] = 'कृपया संदेश दर्ज करें।';
        }
        
        // Rate limiting
        if (!checkRateLimit('contact_' . getClientIP(), 3, 300)) {
            $error_messages[] = 'बहुत सारे अनुरोध। कृपया 5 मिनट बाद पुनः प्रयास करें।';
        }
        
        if (empty($error_messages)) {
            try {
                $db = Config::getDB();
                $stmt = $db->prepare("INSERT INTO contact_inquiries (name, email, phone, subject, message, service_interest) VALUES (?, ?, ?, ?, ?, ?)");
                
                if ($stmt->execute([$name, $email, $phone, $subject, $message, $service_interest])) {
                    $success_message = 'आपका संदेश सफलतापूर्वक भेजा गया! हम 24 घंटे के अंदर आपसे संपर्क करेंगे।';
                    
                    // Send auto-reply email (if email system is configured)
                    // $email_service = new Email();
                    // $email_service->sendContactAutoReply($email, $name);
                    
                    // Clear form data
                    $name = $email = $phone = $subject = $message = $service_interest = '';
                } else {
                    $error_messages[] = 'संदेश भेजने में त्रुटि। कृपया बाद में पुनः प्रयास करें।';
                }
            } catch (Exception $e) {
                $error_messages[] = 'डेटाबेस त्रुटि। कृपया बाद में पुनः प्रयास करें।';
                error_log('Contact form error: ' . $e->getMessage());
            }
        }
    }
}

include 'includes/header.php';
?>

<div class="container mt-5 pt-5">
    <!-- Page Header -->
    <div class="row mb-5">
        <div class="col-12">
            <div class="text-center">
                <h1 class="display-4 fw-bold">संपर्क करें</h1>
                <p class="lead text-muted">हमसे जुड़ें और अपने सपनों की वेबसाइट बनवाएं</p>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Contact Form -->
        <div class="col-lg-8">
            <div class="card shadow-lg border-0">
                <div class="card-header bg-primary text-white py-4">
                    <h3 class="mb-0">
                        <i class="fas fa-envelope me-2"></i>
                        संदेश भेजें
                    </h3>
                </div>
                <div class="card-body p-4">
                    <?php if (!empty($error_messages)): ?>
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-circle me-2"></i>
                            <ul class="mb-0">
                                <?php foreach ($error_messages as $error): ?>
                                    <li><?php echo $error; ?></li>
                                <?php endforeach; ?>
                            </ul>
                        </div>
                    <?php endif; ?>

                    <?php if ($success_message): ?>
                        <div class="alert alert-success">
                            <i class="fas fa-check-circle me-2"></i>
                            <?php echo $success_message; ?>
                        </div>
                    <?php endif; ?>

                    <form method="POST" action="" id="contactForm">
                        <input type="hidden" name="csrf_token" value="<?php echo $_SESSION['csrf_token']; ?>">
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="name" class="form-label">
                                    <i class="fas fa-user me-1"></i>
                                    पूरा नाम *
                                </label>
                                <input type="text" class="form-control" id="name" name="name" 
                                       value="<?php echo htmlspecialchars($name ?? ''); ?>" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="email" class="form-label">
                                    <i class="fas fa-envelope me-1"></i>
                                    ईमेल पता *
                                </label>
                                <input type="email" class="form-control" id="email" name="email" 
                                       value="<?php echo htmlspecialchars($email ?? ''); ?>" required>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="phone" class="form-label">
                                    <i class="fas fa-phone me-1"></i>
                                    फोन नंबर
                                </label>
                                <input type="tel" class="form-control" id="phone" name="phone" 
                                       value="<?php echo htmlspecialchars($phone ?? ''); ?>" 
                                       placeholder="+91-9876543210">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="service_interest" class="form-label">
                                    <i class="fas fa-cogs me-1"></i>
                                    रुचि की सेवा
                                </label>
                                <select class="form-select" id="service_interest" name="service_interest">
                                    <option value="">सेवा चुनें</option>
                                    <option value="business" <?php echo ($service_interest ?? '') === 'business' ? 'selected' : ''; ?>>
                                        बिजनेस वेबसाइट (₹15,000)
                                    </option>
                                    <option value="ecommerce" <?php echo ($service_interest ?? '') === 'ecommerce' ? 'selected' : ''; ?>>
                                        ई-कॉमर्स वेबसाइट (₹75,000)
                                    </option>
                                    <option value="custom" <?php echo ($service_interest ?? '') === 'custom' ? 'selected' : ''; ?>>
                                        कस्टम डेवलपमेंट
                                    </option>
                                    <option value="consultation" <?php echo ($service_interest ?? '') === 'consultation' ? 'selected' : ''; ?>>
                                        सलाह/परामर्श
                                    </option>
                                    <option value="other" <?php echo ($service_interest ?? '') === 'other' ? 'selected' : ''; ?>>
                                        अन्य
                                    </option>
                                </select>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="subject" class="form-label">
                                <i class="fas fa-tag me-1"></i>
                                विषय *
                            </label>
                            <input type="text" class="form-control" id="subject" name="subject" 
                                   value="<?php echo htmlspecialchars($subject ?? ''); ?>" 
                                   placeholder="आपकी पूछताछ का विषय" required>
                        </div>
                        
                        <div class="mb-4">
                            <label for="message" class="form-label">
                                <i class="fas fa-comment me-1"></i>
                                संदेश *
                            </label>
                            <textarea class="form-control" id="message" name="message" rows="6" 
                                      placeholder="कृपया अपनी आवश्यकताओं का विस्तार से वर्णन करें..." required><?php echo htmlspecialchars($message ?? ''); ?></textarea>
                            <div class="form-text">
                                कम से कम 20 अक्षर लिखें। जितना विस्तार से बताएंगे, उतना बेहतर सुझाव दे सकेंगे।
                            </div>
                        </div>
                        
                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary btn-lg">
                                <i class="fas fa-paper-plane me-2"></i>
                                संदेश भेजें
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Contact Information -->
        <div class="col-lg-4">
            <!-- Contact Details -->
            <div class="card shadow-lg border-0 mb-4">
                <div class="card-header bg-success text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-address-book me-2"></i>
                        संपर्क विवरण
                    </h5>
                </div>
                <div class="card-body">
                    <div class="contact-item mb-3">
                        <div class="d-flex align-items-center">
                            <div class="contact-icon bg-primary text-white rounded-circle me-3">
                                <i class="fas fa-envelope"></i>
                            </div>
                            <div>
                                <h6 class="mb-1">ईमेल</h6>
                                <a href="mailto:<?php echo Config::CONTACT_EMAIL; ?>" class="text-decoration-none">
                                    <?php echo Config::CONTACT_EMAIL; ?>
                                </a>
                            </div>
                        </div>
                    </div>
                    
                    <div class="contact-item mb-3">
                        <div class="d-flex align-items-center">
                            <div class="contact-icon bg-success text-white rounded-circle me-3">
                                <i class="fas fa-phone"></i>
                            </div>
                            <div>
                                <h6 class="mb-1">फोन</h6>
                                <a href="tel:<?php echo Config::CONTACT_PHONE; ?>" class="text-decoration-none">
                                    <?php echo Config::CONTACT_PHONE; ?>
                                </a>
                            </div>
                        </div>
                    </div>
                    
                    <div class="contact-item mb-3">
                        <div class="d-flex align-items-center">
                            <div class="contact-icon bg-success text-white rounded-circle me-3">
                                <i class="fab fa-whatsapp"></i>
                            </div>
                            <div>
                                <h6 class="mb-1">WhatsApp</h6>
                                <a href="https://wa.me/<?php echo str_replace(['+', '-', ' '], '', Config::WHATSAPP_NUMBER); ?>?text=नमस्ते! मुझे वेब डेवलपमेंट सेवाओं के बारे में जानकारी चाहिए।" 
                                   target="_blank" class="text-decoration-none">
                                    <?php echo Config::WHATSAPP_NUMBER; ?>
                                </a>
                            </div>
                        </div>
                    </div>
                    
                    <div class="contact-item">
                        <div class="d-flex align-items-center">
                            <div class="contact-icon bg-info text-white rounded-circle me-3">
                                <i class="fas fa-clock"></i>
                            </div>
                            <div>
                                <h6 class="mb-1">कार्य समय</h6>
                                <small class="text-muted">
                                    सोमवार - शनिवार<br>
                                    सुबह 9:00 - रात 8:00
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Quick Services -->
            <div class="card shadow-lg border-0 mb-4">
                <div class="card-header bg-warning text-dark">
                    <h5 class="mb-0">
                        <i class="fas fa-rocket me-2"></i>
                        त्वरित सेवाएं
                    </h5>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="services.php?package=business" class="btn btn-outline-primary btn-sm">
                            <i class="fas fa-briefcase me-2"></i>
                            बिजनेस वेबसाइट
                        </a>
                        <a href="services.php?package=ecommerce" class="btn btn-outline-success btn-sm">
                            <i class="fas fa-store me-2"></i>
                            ई-कॉमर्स स्टोर
                        </a>
                        <a href="services.php?package=custom" class="btn btn-outline-info btn-sm">
                            <i class="fas fa-cogs me-2"></i>
                            कस्टम डेवलपमेंट
                        </a>
                    </div>
                </div>
            </div>

            <!-- FAQ -->
            <div class="card shadow-lg border-0">
                <div class="card-header bg-info text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-question-circle me-2"></i>
                        अक्सर पूछे जाने वाले प्रश्न
                    </h5>
                </div>
                <div class="card-body">
                    <div class="accordion accordion-flush" id="faqAccordion">
                        <div class="accordion-item">
                            <h2 class="accordion-header">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faq1">
                                    वेबसाइट बनने में कितना समय लगता है?
                                </button>
                            </h2>
                            <div id="faq1" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                                <div class="accordion-body">
                                    बिजनेस वेबसाइट 7-10 दिन, ई-कॉमर्स 15-20 दिन में तैयार हो जाती है।
                                </div>
                            </div>
                        </div>
                        
                        <div class="accordion-item">
                            <h2 class="accordion-header">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faq2">
                                    क्या डोमेन और होस्टिंग मुफ्त है?
                                </button>
                            </h2>
                            <div id="faq2" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                                <div class="accordion-body">
                                    हां, पहले साल के लिए डोमेन और होस्टिंग बिल्कुल मुफ्त है।
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.contact-icon {
    width: 50px;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.contact-item {
    padding: 15px 0;
    border-bottom: 1px solid #f0f0f0;
}

.contact-item:last-child {
    border-bottom: none;
}

.accordion-button:not(.collapsed) {
    background-color: #e7f3ff;
    color: #0066cc;
}
</style>

<script>
// Form validation
document.getElementById('contactForm').addEventListener('submit', function(e) {
    const message = document.getElementById('message').value.trim();
    
    if (message.length < 20) {
        e.preventDefault();
        alert('कृपया कम से कम 20 अक्षर का संदेश लिखें।');
        return false;
    }
    
    // Show loading state
    const submitBtn = this.querySelector('button[type="submit"]');
    const originalText = submitBtn.innerHTML;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>भेजा जा रहा है...';
    submitBtn.disabled = true;
    
    // Re-enable button after 10 seconds (in case of issues)
    setTimeout(() => {
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;
    }, 10000);
});

// Character counter for message
document.getElementById('message').addEventListener('input', function() {
    const length = this.value.length;
    const minLength = 20;
    
    let counter = document.getElementById('char-counter');
    if (!counter) {
        counter = document.createElement('div');
        counter.id = 'char-counter';
        counter.className = 'form-text';
        this.parentNode.appendChild(counter);
    }
    
    if (length < minLength) {
        counter.innerHTML = `${length}/${minLength} अक्षर (कम से कम ${minLength} अक्षर आवश्यक)`;
        counter.className = 'form-text text-warning';
    } else {
        counter.innerHTML = `${length} अक्षर - बहुत अच्छा!`;
        counter.className = 'form-text text-success';
    }
});
</script>

<?php include 'includes/footer.php'; ?>
