<?php
require_once 'config/database.php';

class Order {
    private $conn;
    private $table_name = "orders";
    
    public $id;
    public $order_number;
    public $user_id;
    public $service_id;
    public $status;
    public $total_amount;
    public $payment_status;
    public $payment_id;
    public $razorpay_order_id;
    public $razorpay_payment_id;
    public $client_requirements;
    public $admin_notes;
    public $estimated_delivery;
    public $actual_delivery;
    public $created_at;
    public $updated_at;
    
    public function __construct() {
        $this->conn = Config::getDB();
    }
    
    // Create new order
    public function create() {
        $query = "INSERT INTO " . $this->table_name . " 
                 (order_number, user_id, service_id, status, total_amount, payment_status, client_requirements) 
                 VALUES (?, ?, ?, ?, ?, ?, ?)";
        
        $stmt = $this->conn->prepare($query);
        
        // Generate order number if not set
        if (empty($this->order_number)) {
            $this->order_number = $this->generateOrderNumber();
        }
        
        $stmt->bindParam(1, $this->order_number);
        $stmt->bindParam(2, $this->user_id);
        $stmt->bindParam(3, $this->service_id);
        $stmt->bindParam(4, $this->status);
        $stmt->bindParam(5, $this->total_amount);
        $stmt->bindParam(6, $this->payment_status);
        $stmt->bindParam(7, $this->client_requirements);
        
        if ($stmt->execute()) {
            $this->id = $this->conn->lastInsertId();
            $this->logStatusChange($this->status, 'Order created');
            return true;
        }
        return false;
    }
    
    // Get order by ID
    public function getById($id) {
        $query = "SELECT o.*, s.name as service_name, s.name_hindi as service_name_hindi, 
                         u.full_name as customer_name, u.email as customer_email, u.phone as customer_phone
                 FROM " . $this->table_name . " o
                 LEFT JOIN services s ON o.service_id = s.id
                 LEFT JOIN users u ON o.user_id = u.id
                 WHERE o.id = ?";
        
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(1, $id);
        $stmt->execute();
        
        if ($stmt->rowCount() > 0) {
            $row = $stmt->fetch(PDO::FETCH_ASSOC);
            $this->id = $row['id'];
            $this->order_number = $row['order_number'];
            $this->user_id = $row['user_id'];
            $this->service_id = $row['service_id'];
            $this->status = $row['status'];
            $this->total_amount = $row['total_amount'];
            $this->payment_status = $row['payment_status'];
            $this->payment_id = $row['payment_id'];
            $this->razorpay_order_id = $row['razorpay_order_id'];
            $this->razorpay_payment_id = $row['razorpay_payment_id'];
            $this->client_requirements = $row['client_requirements'];
            $this->admin_notes = $row['admin_notes'];
            $this->estimated_delivery = $row['estimated_delivery'];
            $this->actual_delivery = $row['actual_delivery'];
            $this->created_at = $row['created_at'];
            $this->updated_at = $row['updated_at'];
            return $row;
        }
        return false;
    }
    
    // Get order by order number
    public function getByOrderNumber($order_number) {
        $query = "SELECT * FROM " . $this->table_name . " WHERE order_number = ?";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(1, $order_number);
        $stmt->execute();
        
        if ($stmt->rowCount() > 0) {
            $row = $stmt->fetch(PDO::FETCH_ASSOC);
            $this->id = $row['id'];
            $this->order_number = $row['order_number'];
            $this->user_id = $row['user_id'];
            $this->service_id = $row['service_id'];
            $this->status = $row['status'];
            $this->total_amount = $row['total_amount'];
            $this->payment_status = $row['payment_status'];
            $this->payment_id = $row['payment_id'];
            $this->razorpay_order_id = $row['razorpay_order_id'];
            $this->razorpay_payment_id = $row['razorpay_payment_id'];
            $this->client_requirements = $row['client_requirements'];
            $this->admin_notes = $row['admin_notes'];
            $this->estimated_delivery = $row['estimated_delivery'];
            $this->actual_delivery = $row['actual_delivery'];
            $this->created_at = $row['created_at'];
            $this->updated_at = $row['updated_at'];
            return true;
        }
        return false;
    }
    
    // Update order status
    public function updateStatus($new_status, $notes = '', $changed_by = null) {
        $query = "UPDATE " . $this->table_name . " 
                 SET status = ?, updated_at = CURRENT_TIMESTAMP 
                 WHERE id = ?";
        
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(1, $new_status);
        $stmt->bindParam(2, $this->id);
        
        if ($stmt->execute()) {
            $old_status = $this->status;
            $this->status = $new_status;
            $this->logStatusChange($new_status, $notes, $changed_by);
            
            // Send email notification
            $this->sendStatusUpdateEmail($old_status, $new_status);
            
            return true;
        }
        return false;
    }
    
    // Update payment status
    public function updatePaymentStatus($payment_status, $payment_id = '', $razorpay_payment_id = '') {
        $query = "UPDATE " . $this->table_name . " 
                 SET payment_status = ?, payment_id = ?, razorpay_payment_id = ?, updated_at = CURRENT_TIMESTAMP 
                 WHERE id = ?";
        
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(1, $payment_status);
        $stmt->bindParam(2, $payment_id);
        $stmt->bindParam(3, $razorpay_payment_id);
        $stmt->bindParam(4, $this->id);
        
        if ($stmt->execute()) {
            $this->payment_status = $payment_status;
            $this->payment_id = $payment_id;
            $this->razorpay_payment_id = $razorpay_payment_id;
            
            // If payment is successful, update order status to confirmed
            if ($payment_status === 'paid' && $this->status === 'pending') {
                $this->updateStatus('confirmed', 'Payment received successfully');
            }
            
            return true;
        }
        return false;
    }
    
    // Get orders by user
    public function getByUser($user_id, $limit = 20, $offset = 0) {
        $query = "SELECT o.*, s.name as service_name, s.name_hindi as service_name_hindi
                 FROM " . $this->table_name . " o
                 LEFT JOIN services s ON o.service_id = s.id
                 WHERE o.user_id = ?
                 ORDER BY o.created_at DESC
                 LIMIT ? OFFSET ?";
        
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(1, $user_id);
        $stmt->bindParam(2, $limit, PDO::PARAM_INT);
        $stmt->bindParam(3, $offset, PDO::PARAM_INT);
        $stmt->execute();
        
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    // Get all orders (admin)
    public function getAll($limit = 20, $offset = 0, $status = '', $payment_status = '') {
        $query = "SELECT o.*, s.name as service_name, s.name_hindi as service_name_hindi,
                         u.full_name as customer_name, u.email as customer_email
                 FROM " . $this->table_name . " o
                 LEFT JOIN services s ON o.service_id = s.id
                 LEFT JOIN users u ON o.user_id = u.id
                 WHERE 1=1";
        
        $params = [];
        
        if (!empty($status)) {
            $query .= " AND o.status = ?";
            $params[] = $status;
        }
        
        if (!empty($payment_status)) {
            $query .= " AND o.payment_status = ?";
            $params[] = $payment_status;
        }
        
        $query .= " ORDER BY o.created_at DESC LIMIT ? OFFSET ?";
        $params[] = $limit;
        $params[] = $offset;
        
        $stmt = $this->conn->prepare($query);
        $stmt->execute($params);
        
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    // Get order statistics
    public function getStats() {
        $stats = [];
        
        // Total orders
        $query = "SELECT COUNT(*) as total FROM " . $this->table_name;
        $stmt = $this->conn->prepare($query);
        $stmt->execute();
        $stats['total_orders'] = $stmt->fetch()['total'];
        
        // Pending orders
        $query = "SELECT COUNT(*) as pending FROM " . $this->table_name . " WHERE status = 'pending'";
        $stmt = $this->conn->prepare($query);
        $stmt->execute();
        $stats['pending_orders'] = $stmt->fetch()['pending'];
        
        // Completed orders
        $query = "SELECT COUNT(*) as completed FROM " . $this->table_name . " WHERE status = 'completed'";
        $stmt = $this->conn->prepare($query);
        $stmt->execute();
        $stats['completed_orders'] = $stmt->fetch()['completed'];
        
        // Total revenue
        $query = "SELECT SUM(total_amount) as revenue FROM " . $this->table_name . " WHERE payment_status = 'paid'";
        $stmt = $this->conn->prepare($query);
        $stmt->execute();
        $stats['total_revenue'] = $stmt->fetch()['revenue'] ?? 0;
        
        // This month's orders
        $query = "SELECT COUNT(*) as this_month FROM " . $this->table_name . " 
                 WHERE MONTH(created_at) = MONTH(CURRENT_DATE()) 
                 AND YEAR(created_at) = YEAR(CURRENT_DATE())";
        $stmt = $this->conn->prepare($query);
        $stmt->execute();
        $stats['orders_this_month'] = $stmt->fetch()['this_month'];
        
        return $stats;
    }
    
    // Generate unique order number
    private function generateOrderNumber() {
        do {
            $order_number = 'ORD' . date('Ymd') . rand(1000, 9999);
            $query = "SELECT id FROM " . $this->table_name . " WHERE order_number = ?";
            $stmt = $this->conn->prepare($query);
            $stmt->execute([$order_number]);
        } while ($stmt->rowCount() > 0);
        
        return $order_number;
    }
    
    // Log status change
    private function logStatusChange($status, $notes = '', $changed_by = null) {
        try {
            $query = "INSERT INTO order_status_history (order_id, status, notes, changed_by) 
                     VALUES (?, ?, ?, ?)";
            $stmt = $this->conn->prepare($query);
            $changed_by = $changed_by ?? $_SESSION['user_id'] ?? null;
            $stmt->execute([$this->id, $status, $notes, $changed_by]);
        } catch(Exception $e) {
            error_log("Status logging failed: " . $e->getMessage());
        }
    }
    
    // Send status update email
    private function sendStatusUpdateEmail($old_status, $new_status) {
        // This will be implemented with the email system
        // For now, just log the action
        error_log("Order {$this->order_number} status changed from {$old_status} to {$new_status}");
    }
    
    // Get order status history
    public function getStatusHistory($order_id) {
        $query = "SELECT osh.*, u.full_name as changed_by_name
                 FROM order_status_history osh
                 LEFT JOIN users u ON osh.changed_by = u.id
                 WHERE osh.order_id = ?
                 ORDER BY osh.created_at DESC";
        
        $stmt = $this->conn->prepare($query);
        $stmt->execute([$order_id]);
        
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    // Update admin notes
    public function updateAdminNotes($notes) {
        $query = "UPDATE " . $this->table_name . " 
                 SET admin_notes = ?, updated_at = CURRENT_TIMESTAMP 
                 WHERE id = ?";
        
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(1, $notes);
        $stmt->bindParam(2, $this->id);
        
        if ($stmt->execute()) {
            $this->admin_notes = $notes;
            return true;
        }
        return false;
    }
    
    // Set estimated delivery date
    public function setEstimatedDelivery($date) {
        $query = "UPDATE " . $this->table_name . " 
                 SET estimated_delivery = ?, updated_at = CURRENT_TIMESTAMP 
                 WHERE id = ?";
        
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(1, $date);
        $stmt->bindParam(2, $this->id);
        
        if ($stmt->execute()) {
            $this->estimated_delivery = $date;
            return true;
        }
        return false;
    }
    
    // Mark as delivered
    public function markAsDelivered() {
        $query = "UPDATE " . $this->table_name . " 
                 SET status = 'completed', actual_delivery = CURRENT_DATE(), updated_at = CURRENT_TIMESTAMP 
                 WHERE id = ?";
        
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(1, $this->id);
        
        if ($stmt->execute()) {
            $this->status = 'completed';
            $this->actual_delivery = date('Y-m-d');
            $this->logStatusChange('completed', 'Project delivered successfully');
            return true;
        }
        return false;
    }
}
?>
