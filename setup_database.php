<?php
// Database setup script for XAMPP
// Run this file once to create database and tables

// Database configuration for setup
$host = 'localhost';
$username = 'root';
$password = '';
$database = 'web_dev_services';

try {
    // Connect to MySQL server (without database)
    $pdo = new PDO("mysql:host=$host", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // Create database
    $pdo->exec("CREATE DATABASE IF NOT EXISTS `$database` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
    echo "✅ Database '$database' created successfully.<br>";
    
    // Connect to the created database
    $pdo = new PDO("mysql:host=$host;dbname=$database", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // Create users table
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS `users` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `username` varchar(50) NOT NULL UNIQUE,
            `email` varchar(100) NOT NULL UNIQUE,
            `password` varchar(255) NOT NULL,
            `full_name` varchar(100) NOT NULL,
            `phone` varchar(20) DEFAULT NULL,
            `role` enum('admin','client') NOT NULL DEFAULT 'client',
            `status` enum('active','inactive','suspended') NOT NULL DEFAULT 'active',
            `email_verified` tinyint(1) NOT NULL DEFAULT 0,
            `verification_token` varchar(100) DEFAULT NULL,
            `reset_token` varchar(100) DEFAULT NULL,
            `reset_expires` timestamp NULL DEFAULT NULL,
            `last_login` timestamp NULL DEFAULT NULL,
            `login_attempts` int(11) NOT NULL DEFAULT 0,
            `locked_until` timestamp NULL DEFAULT NULL,
            `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
            `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            KEY `idx_email` (`email`),
            KEY `idx_username` (`username`),
            KEY `idx_status` (`status`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");
    echo "✅ Users table created successfully.<br>";
    
    // Create services table
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS `services` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `name` varchar(100) NOT NULL,
            `name_hindi` varchar(100) NOT NULL,
            `description` text,
            `description_hindi` text,
            `price` decimal(10,2) NOT NULL DEFAULT 0.00,
            `features` json DEFAULT NULL,
            `features_hindi` json DEFAULT NULL,
            `delivery_days` int(11) NOT NULL DEFAULT 7,
            `revisions_included` int(11) NOT NULL DEFAULT 2,
            `is_active` tinyint(1) NOT NULL DEFAULT 1,
            `display_order` int(11) NOT NULL DEFAULT 0,
            `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
            `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            KEY `idx_active` (`is_active`),
            KEY `idx_order` (`display_order`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");
    echo "✅ Services table created successfully.<br>";
    
    // Create contact_inquiries table
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS `contact_inquiries` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `name` varchar(100) NOT NULL,
            `email` varchar(100) NOT NULL,
            `phone` varchar(20) DEFAULT NULL,
            `subject` varchar(200) NOT NULL,
            `message` text NOT NULL,
            `service_interest` varchar(50) DEFAULT NULL,
            `status` enum('new','replied','closed') NOT NULL DEFAULT 'new',
            `admin_reply` text,
            `replied_at` timestamp NULL DEFAULT NULL,
            `replied_by` int(11) DEFAULT NULL,
            `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            KEY `idx_status` (`status`),
            KEY `idx_email` (`email`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");
    echo "✅ Contact inquiries table created successfully.<br>";
    
    // Create orders table
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS `orders` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `order_number` varchar(20) NOT NULL UNIQUE,
            `user_id` int(11) NOT NULL,
            `service_id` int(11) NOT NULL,
            `status` enum('pending','confirmed','in_progress','completed','cancelled') NOT NULL DEFAULT 'pending',
            `total_amount` decimal(10,2) NOT NULL,
            `payment_status` enum('pending','paid','failed','refunded') NOT NULL DEFAULT 'pending',
            `payment_id` varchar(100) DEFAULT NULL,
            `razorpay_order_id` varchar(100) DEFAULT NULL,
            `razorpay_payment_id` varchar(100) DEFAULT NULL,
            `client_requirements` text,
            `admin_notes` text,
            `estimated_delivery` date DEFAULT NULL,
            `actual_delivery` date DEFAULT NULL,
            `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
            `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            KEY `idx_user` (`user_id`),
            KEY `idx_service` (`service_id`),
            KEY `idx_status` (`status`),
            KEY `idx_payment_status` (`payment_status`),
            KEY `idx_order_number` (`order_number`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");
    echo "✅ Orders table created successfully.<br>";
    
    // Create blog_categories table
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS `blog_categories` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `name` varchar(100) NOT NULL,
            `name_hindi` varchar(100) NOT NULL,
            `slug` varchar(100) NOT NULL UNIQUE,
            `description` text,
            `description_hindi` text,
            `is_active` tinyint(1) NOT NULL DEFAULT 1,
            `display_order` int(11) NOT NULL DEFAULT 0,
            `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            KEY `idx_slug` (`slug`),
            KEY `idx_active` (`is_active`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");
    echo "✅ Blog categories table created successfully.<br>";
    
    // Create blog_posts table
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS `blog_posts` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `title` varchar(200) NOT NULL,
            `title_hindi` varchar(200) NOT NULL,
            `slug` varchar(200) NOT NULL UNIQUE,
            `content` longtext,
            `content_hindi` longtext,
            `excerpt` text,
            `excerpt_hindi` text,
            `featured_image` varchar(255) DEFAULT NULL,
            `category_id` int(11) DEFAULT NULL,
            `author_id` int(11) NOT NULL,
            `status` enum('draft','published','archived') NOT NULL DEFAULT 'draft',
            `meta_title` varchar(200) DEFAULT NULL,
            `meta_description` varchar(300) DEFAULT NULL,
            `tags` varchar(500) DEFAULT NULL,
            `view_count` int(11) NOT NULL DEFAULT 0,
            `published_at` timestamp NULL DEFAULT NULL,
            `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
            `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            KEY `idx_slug` (`slug`),
            KEY `idx_status` (`status`),
            KEY `idx_category` (`category_id`),
            KEY `idx_author` (`author_id`),
            KEY `idx_published` (`published_at`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");
    echo "✅ Blog posts table created successfully.<br>";
    
    // Create portfolio table
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS `portfolio` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `title` varchar(200) NOT NULL,
            `title_hindi` varchar(200) NOT NULL,
            `description` text,
            `description_hindi` text,
            `project_type` enum('business','ecommerce','custom') NOT NULL,
            `client_name` varchar(100) DEFAULT NULL,
            `project_url` varchar(255) DEFAULT NULL,
            `image_gallery` json DEFAULT NULL,
            `technologies_used` json DEFAULT NULL,
            `completion_date` date DEFAULT NULL,
            `is_featured` tinyint(1) NOT NULL DEFAULT 0,
            `display_order` int(11) NOT NULL DEFAULT 0,
            `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            KEY `idx_type` (`project_type`),
            KEY `idx_featured` (`is_featured`),
            KEY `idx_order` (`display_order`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");
    echo "✅ Portfolio table created successfully.<br>";
    
    // Create newsletter_subscribers table
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS `newsletter_subscribers` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `email` varchar(100) NOT NULL UNIQUE,
            `name` varchar(100) DEFAULT NULL,
            `status` enum('active','unsubscribed') NOT NULL DEFAULT 'active',
            `subscribed_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
            `unsubscribed_at` timestamp NULL DEFAULT NULL,
            PRIMARY KEY (`id`),
            KEY `idx_email` (`email`),
            KEY `idx_status` (`status`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");
    echo "✅ Newsletter subscribers table created successfully.<br>";
    
    // Insert default admin user (password: admin123)
    $admin_password = password_hash('admin123', PASSWORD_DEFAULT);
    $pdo->exec("
        INSERT IGNORE INTO `users` (`username`, `email`, `password`, `full_name`, `role`, `status`, `email_verified`) 
        VALUES ('admin', '<EMAIL>', '$admin_password', 'Administrator', 'admin', 'active', 1)
    ");
    echo "✅ Default admin user created (username: admin, password: admin123).<br>";
    
    // Insert default services
    $pdo->exec("
        INSERT IGNORE INTO `services` (`id`, `name`, `name_hindi`, `description_hindi`, `price`, `features_hindi`, `delivery_days`, `revisions_included`) VALUES
        (1, 'Business Premium Package', 'बिजनेस प्रीमियम पैकेज', 'पूर्ण व्यावसायिक वेबसाइट समाधान', 15000.00, '[\"मुफ्त डोमेन और होस्टिंग सेटअप\", \"सुरक्षित प्रमाणीकरण के साथ मल्टी-पेज वेबसाइट\", \"क्वेरी डैशबोर्ड\", \"स्वचालित ईमेल भेजने वाला (500/माह)\", \"reCAPTCHA एकीकरण\", \"ऑन-पेज SEO अनुकूलन\", \"Google Analytics एकीकरण\", \"WhatsApp/फोन/ईमेल सहायता लिंक\"]', 10, 2),
        (2, 'E-Commerce Package', 'ई-कॉमर्स पैकेज', 'पूर्ण ई-कॉमर्स समाधान', 75000.00, '[\"मुफ्त डोमेन और होस्टिंग सेटअप\", \"पूर्ण ई-कॉमर्स स्टोरफ्रंट\", \"तीन अलग डैशबोर्ड (एडमिन/विक्रेता/ग्राहक)\", \"भूमिका-आधारित पहुंच नियंत्रण\", \"Razorpay पेमेंट गेटवे एकीकरण\", \"स्वचालित ईमेल सिस्टम (500/माह)\", \"महत्वपूर्ण फॉर्म पर reCAPTCHA\", \"ई-कॉमर्स SEO + Analytics\"]', 20, 3),
        (3, 'Custom Development', 'कस्टम डेवलपमेंट', 'कस्टम वेब डेवलपमेंट समाधान', 0.00, '[\"कस्टम CRM समाधान\", \"बुकिंग सिस्टम\", \"लर्निंग मैनेजमेंट सिस्टम (LMS)\", \"पोर्टल और ऑटोमेशन\", \"मॉड्यूलर मूल्य निर्धारण\", \"Google Analytics एकीकरण\", \"आवश्यक ऑन-पेज SEO\", \"आवश्यकता-आधारित स्कोपिंग\"]', 30, 5)
    ");
    echo "✅ Default services inserted successfully.<br>";
    
    // Insert default blog categories
    $pdo->exec("
        INSERT IGNORE INTO `blog_categories` (`name`, `name_hindi`, `slug`, `description_hindi`) VALUES
        ('Web Development', 'वेब डेवलपमेंट', 'web-development', 'वेब डेवलपमेंट से संबंधित लेख'),
        ('Digital Marketing', 'डिजिटल मार्केटिंग', 'digital-marketing', 'डिजिटल मार्केटिंग टिप्स और ट्रिक्स'),
        ('SEO Tips', 'SEO टिप्स', 'seo-tips', 'सर्च इंजन ऑप्टिमाइजेशन गाइड'),
        ('Business Growth', 'व्यवसाय विकास', 'business-growth', 'व्यवसाय बढ़ाने की रणनीतियां')
    ");
    echo "✅ Default blog categories inserted successfully.<br>";
    
    // Insert sample portfolio items
    $pdo->exec("
        INSERT IGNORE INTO `portfolio` (`title`, `title_hindi`, `description_hindi`, `project_type`, `client_name`, `technologies_used`, `completion_date`, `is_featured`) VALUES
        ('Modern Business Website', 'आधुनिक व्यावसायिक वेबसाइट', 'एक स्थानीय व्यवसाय के लिए आधुनिक और उत्तरदायी वेबसाइट', 'business', 'ABC कंपनी', '[\"HTML5\", \"CSS3\", \"JavaScript\", \"PHP\", \"MySQL\"]', '2024-01-15', 1),
        ('E-commerce Store', 'ई-कॉमर्स स्टोर', 'पूर्ण ऑनलाइन शॉपिंग स्टोर पेमेंट गेटवे के साथ', 'ecommerce', 'XYZ फैशन', '[\"PHP\", \"MySQL\", \"Bootstrap\", \"Razorpay\", \"JavaScript\"]', '2024-02-20', 1),
        ('Custom CRM System', 'कस्टम CRM सिस्टम', 'ग्राहक संबंध प्रबंधन के लिए कस्टम सिस्टम', 'custom', 'PQR सर्विसेज', '[\"PHP\", \"MySQL\", \"AJAX\", \"Chart.js\", \"Bootstrap\"]', '2024-03-10', 0)
    ");
    echo "✅ Sample portfolio items inserted successfully.<br>";
    
    echo "<br><h2>🎉 Database setup completed successfully!</h2>";
    echo "<p><strong>Next steps:</strong></p>";
    echo "<ul>";
    echo "<li>✅ Database and tables created</li>";
    echo "<li>✅ Default admin user created (username: admin, password: admin123)</li>";
    echo "<li>✅ Sample data inserted</li>";
    echo "<li>🔗 <a href='index.php'>Go to Homepage</a></li>";
    echo "<li>🔗 <a href='auth/login.php'>Login as Admin</a></li>";
    echo "</ul>";
    
} catch(PDOException $e) {
    echo "❌ Error: " . $e->getMessage();
}
?>
