<?php
// Database configuration
class Database {
    private $host = 'localhost';
    private $db_name = 'webdev_services';
    private $username = 'webev_services-user';
    private $password = '2543@4321';
    private $charset = 'utf8mb4';
    public $conn;

    public function getConnection() {
        $this->conn = null;
        
        try {
            $dsn = "mysql:host=" . $this->host . ";dbname=" . $this->db_name . ";charset=" . $this->charset;
            $this->conn = new PDO($dsn, $this->username, $this->password);
            $this->conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
            $this->conn->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);
        } catch(PDOException $exception) {
            echo "Connection error: " . $exception->getMessage();
        }
        
        return $this->conn;
    }
}

// Site configuration
class Config {
    // Site settings
    const SITE_NAME = 'websitedeveloper0002.in';
    const SITE_NAME_HINDI = 'वेबसाइटडेवलपर0002.in';
    const SITE_URL = 'http://localhost/prompts';
    const ADMIN_EMAIL = '<EMAIL>';
    
    // Contact information
    const CONTACT_EMAIL = '<EMAIL>';
    const CONTACT_PHONE = '+91-7084066581';
    const WHATSAPP_NUMBER = '+91-7084066581';
    
    // Razorpay configuration (to be filled with actual keys)
    const RAZORPAY_KEY_ID = 'rzp_test_RACm32TqD2EH9W';
    const RAZORPAY_KEY_SECRET = '49AidUquRVA1wQMFJ0q41i82';
    
    // Google services
    const GOOGLE_ANALYTICS_ID = 'G-2GTWXL9YEQ';
    const RECAPTCHA_SITE_KEY = '6L6L4xcrAAAAAAc3vLF2mYPWa0cFzAcAnph2_';
    const RECAPTCHA_SECRET_KEY = '6L6L4xcrAAAAAAQO3unU8Tpsr1Cv39xt41X8VbA';
    
    // Email configuration
    const SMTP_HOST = 'mail.websitedeveloper0002.in';
    const SMTP_PORT = 587;
    const SMTP_USERNAME = '<EMAIL>';
    const SMTP_PASSWORD = 'cIF!T]^zUpt3kf5o';
    const SMTP_FROM_EMAIL = '<EMAIL>';
    const SMTP_FROM_NAME = 'websitedeveloper0002';
    
    // File upload settings
    const UPLOAD_PATH = 'uploads/';
    const MAX_FILE_SIZE = 5242880; // 5MB
    const ALLOWED_IMAGE_TYPES = ['jpg', 'jpeg', 'png', 'gif', 'webp'];
    
    // Pagination
    const POSTS_PER_PAGE = 10;
    const ORDERS_PER_PAGE = 20;
    
    // Security
    const SESSION_TIMEOUT = 3600; // 1 hour
    const PASSWORD_MIN_LENGTH = 8;
    const MAX_LOGIN_ATTEMPTS = 5;
    const LOGIN_LOCKOUT_TIME = 900; // 15 minutes
    
    // Currency
    const CURRENCY = 'INR';
    const CURRENCY_SYMBOL = '₹';
    
    // Language
    const DEFAULT_LANGUAGE = 'hi'; // Hindi
    const SUPPORTED_LANGUAGES = ['hi', 'en'];
    
    // Get database connection
    public static function getDB() {
        $database = new Database();
        return $database->getConnection();
    }
    
    // Get site setting from database
    public static function getSetting($key, $default = '') {
        try {
            $db = self::getDB();
            $stmt = $db->prepare("SELECT setting_value FROM site_settings WHERE setting_key = ?");
            $stmt->execute([$key]);
            $result = $stmt->fetch();
            return $result ? $result['setting_value'] : $default;
        } catch(Exception $e) {
            return $default;
        }
    }
    
    // Update site setting
    public static function updateSetting($key, $value) {
        try {
            $db = self::getDB();
            $stmt = $db->prepare("INSERT INTO site_settings (setting_key, setting_value) VALUES (?, ?) 
                                 ON DUPLICATE KEY UPDATE setting_value = ?");
            return $stmt->execute([$key, $value, $value]);
        } catch(Exception $e) {
            return false;
        }
    }
}

// Error reporting (disable in production)
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Set timezone
date_default_timezone_set('Asia/Kolkata');

// Start session
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Security headers
header('X-Content-Type-Options: nosniff');
header('X-Frame-Options: DENY');
header('X-XSS-Protection: 1; mode=block');

// CSRF token generation
if (!isset($_SESSION['csrf_token'])) {
    $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
}

// Helper function to verify CSRF token
function verifyCsrfToken($token) {
    return isset($_SESSION['csrf_token']) && hash_equals($_SESSION['csrf_token'], $token);
}

// Helper function to sanitize input
function sanitizeInput($data) {
    $data = trim($data);
    $data = stripslashes($data);
    $data = htmlspecialchars($data, ENT_QUOTES, 'UTF-8');
    return $data;
}

// Helper function to validate email
function isValidEmail($email) {
    return filter_var($email, FILTER_VALIDATE_EMAIL) !== false;
}

// Helper function to validate phone number (Indian format)
function isValidPhone($phone) {
    $pattern = '/^(\+91[\-\s]?)?[0]?(91)?[789]\d{9}$/';
    return preg_match($pattern, $phone);
}

// Helper function to generate order number
function generateOrderNumber() {
    return 'ORD' . date('Ymd') . rand(1000, 9999);
}

// Helper function to format currency
function formatCurrency($amount) {
    return Config::CURRENCY_SYMBOL . number_format($amount, 2);
}

// Helper function to get user's IP address
function getUserIP() {
    if (!empty($_SERVER['HTTP_CLIENT_IP'])) {
        return $_SERVER['HTTP_CLIENT_IP'];
    } elseif (!empty($_SERVER['HTTP_X_FORWARDED_FOR'])) {
        return $_SERVER['HTTP_X_FORWARDED_FOR'];
    } else {
        return $_SERVER['REMOTE_ADDR'];
    }
}

// Helper function to log activities
function logActivity($user_id, $action, $details = '') {
    try {
        $db = Config::getDB();
        $stmt = $db->prepare("INSERT INTO activity_logs (user_id, action, details, ip_address) VALUES (?, ?, ?, ?)");
        return $stmt->execute([$user_id, $action, $details, getUserIP()]);
    } catch(Exception $e) {
        return false;
    }
}
?>
