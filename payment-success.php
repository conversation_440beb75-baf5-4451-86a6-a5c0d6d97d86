<?php
require_once 'config/database.php';
require_once 'classes/User.php';
require_once 'classes/Order.php';
require_once 'classes/Payment.php';

// Check if user is logged in
if (!User::isLoggedIn()) {
    header('Location: auth/login.php');
    exit();
}

$page_title = 'पेमेंट सफल - वेब डेवलपमेंट सेवाएं';
$meta_description = 'आपका भुगतान सफलतापूर्वक पूरा हो गया है';

// Handle POST request for payment verification
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    header('Content-Type: application/json');
    
    try {
        $order_number = $_POST['order_number'] ?? '';
        $razorpay_payment_id = $_POST['razorpay_payment_id'] ?? '';
        $razorpay_order_id = $_POST['razorpay_order_id'] ?? '';
        $razorpay_signature = $_POST['razorpay_signature'] ?? '';
        
        if (empty($order_number) || empty($razorpay_payment_id) || empty($razorpay_order_id) || empty($razorpay_signature)) {
            throw new Exception('अधूरी पेमेंट जानकारी');
        }
        
        $payment = new Payment();
        $success = $payment->processPaymentSuccess($order_number, $razorpay_payment_id, $razorpay_order_id, $razorpay_signature);
        
        if ($success) {
            echo json_encode(['success' => true, 'message' => 'पेमेंट सफल']);
        } else {
            throw new Exception('पेमेंट वेरिफिकेशन असफल');
        }
        
    } catch (Exception $e) {
        echo json_encode(['success' => false, 'message' => $e->getMessage()]);
    }
    exit();
}

// Handle GET request for success page display
$order_number = $_GET['order'] ?? '';

if (empty($order_number)) {
    $_SESSION['error_message'] = 'ऑर्डर नंबर नहीं मिला।';
    header('Location: client/dashboard.php');
    exit();
}

// Get order details
$order = new Order();
$order_details = $order->getById($order->getByOrderNumber($order_number) ? $order->id : 0);

if (!$order_details) {
    $_SESSION['error_message'] = 'ऑर्डर नहीं मिला।';
    header('Location: client/dashboard.php');
    exit();
}

// Check if user owns this order
if ($order_details['user_id'] != $_SESSION['user_id']) {
    $_SESSION['error_message'] = 'अनधिकृत पहुंच।';
    header('Location: client/dashboard.php');
    exit();
}

// Check if payment is actually successful
if ($order_details['payment_status'] !== 'paid') {
    $_SESSION['error_message'] = 'पेमेंट अभी भी पेंडिंग है।';
    header('Location: payment.php?order=' . $order_number);
    exit();
}

include 'includes/header.php';
?>

<div class="container mt-5 pt-5">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <!-- Success Message -->
            <div class="card shadow-lg border-0 mb-4">
                <div class="card-body text-center p-5">
                    <div class="success-animation mb-4">
                        <i class="fas fa-check-circle fa-5x text-success"></i>
                    </div>
                    <h2 class="text-success mb-3">पेमेंट सफल!</h2>
                    <p class="lead text-muted mb-4">
                        आपका भुगतान सफलतापूर्वक पूरा हो गया है। हमारी टीम जल्द ही आपके प्रोजेक्ट पर काम शुरू करेगी।
                    </p>
                    
                    <!-- Order Details -->
                    <div class="order-success-details bg-light p-4 rounded mb-4">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <div class="detail-item">
                                    <label class="text-muted small">ऑर्डर नंबर</label>
                                    <div class="fw-bold text-primary h5"><?php echo htmlspecialchars($order_details['order_number']); ?></div>
                                </div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <div class="detail-item">
                                    <label class="text-muted small">भुगतान राशि</label>
                                    <div class="fw-bold text-success h5">₹<?php echo number_format($order_details['total_amount']); ?></div>
                                </div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <div class="detail-item">
                                    <label class="text-muted small">सेवा</label>
                                    <div class="fw-bold"><?php echo htmlspecialchars($order_details['service_name_hindi']); ?></div>
                                </div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <div class="detail-item">
                                    <label class="text-muted small">भुगतान दिनांक</label>
                                    <div class="fw-bold"><?php echo date('d/m/Y H:i', strtotime($order_details['updated_at'])); ?></div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Action Buttons -->
                    <div class="action-buttons">
                        <a href="client/order-details.php?id=<?php echo $order_details['id']; ?>" class="btn btn-primary btn-lg me-3">
                            <i class="fas fa-eye me-2"></i>
                            ऑर्डर विवरण देखें
                        </a>
                        <a href="client/dashboard.php" class="btn btn-outline-primary btn-lg">
                            <i class="fas fa-tachometer-alt me-2"></i>
                            डैशबोर्ड पर जाएं
                        </a>
                    </div>
                </div>
            </div>

            <!-- What's Next Section -->
            <div class="card shadow-lg border-0 mb-4">
                <div class="card-header bg-info text-white">
                    <h4 class="mb-0">
                        <i class="fas fa-road me-2"></i>
                        आगे क्या होगा?
                    </h4>
                </div>
                <div class="card-body">
                    <div class="timeline">
                        <div class="timeline-item completed">
                            <div class="timeline-marker bg-success">
                                <i class="fas fa-check text-white"></i>
                            </div>
                            <div class="timeline-content">
                                <h6 class="mb-1">ऑर्डर प्राप्त और भुगतान पूर्ण</h6>
                                <small class="text-muted">अभी पूरा हुआ</small>
                            </div>
                        </div>
                        
                        <div class="timeline-item active">
                            <div class="timeline-marker bg-primary">
                                <i class="fas fa-cogs text-white"></i>
                            </div>
                            <div class="timeline-content">
                                <h6 class="mb-1">प्रोजेक्ट विश्लेषण और प्लानिंग</h6>
                                <small class="text-muted">24 घंटे के अंदर</small>
                            </div>
                        </div>
                        
                        <div class="timeline-item">
                            <div class="timeline-marker bg-warning">
                                <i class="fas fa-code text-white"></i>
                            </div>
                            <div class="timeline-content">
                                <h6 class="mb-1">डेवलपमेंट शुरू</h6>
                                <small class="text-muted">2-3 दिन में</small>
                            </div>
                        </div>
                        
                        <div class="timeline-item">
                            <div class="timeline-marker bg-info">
                                <i class="fas fa-rocket text-white"></i>
                            </div>
                            <div class="timeline-content">
                                <h6 class="mb-1">प्रोजेक्ट डिलीवरी</h6>
                                <small class="text-muted">
                                    <?php 
                                    $delivery_days = $order_details['service_name'] === 'Business Premium Package' ? '7-10' : 
                                                   ($order_details['service_name'] === 'E-Commerce Package' ? '15-20' : '2-8 सप्ताह');
                                    echo $delivery_days . ' दिन में';
                                    ?>
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Contact Information -->
            <div class="row g-4">
                <div class="col-md-6">
                    <div class="card border-0 shadow-sm">
                        <div class="card-body text-center">
                            <i class="fas fa-envelope fa-3x text-primary mb-3"></i>
                            <h5>ईमेल अपडेट</h5>
                            <p class="text-muted">आपको प्रोजेक्ट की प्रगति के बारे में ईमेल अपडेट मिलते रहेंगे</p>
                            <small class="text-muted"><?php echo htmlspecialchars($_SESSION['email']); ?></small>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card border-0 shadow-sm">
                        <div class="card-body text-center">
                            <i class="fab fa-whatsapp fa-3x text-success mb-3"></i>
                            <h5>WhatsApp सपोर्ट</h5>
                            <p class="text-muted">किसी भी सवाल के लिए हमसे WhatsApp पर संपर्क करें</p>
                            <a href="https://wa.me/<?php echo str_replace(['+', '-', ' '], '', Config::WHATSAPP_NUMBER); ?>?text=नमस्ते! मेरा ऑर्डर नंबर <?php echo $order_details['order_number']; ?> है।" 
                               target="_blank" class="btn btn-success btn-sm">
                                <i class="fab fa-whatsapp me-2"></i>
                                WhatsApp करें
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Invoice Download -->
            <div class="text-center mt-4">
                <a href="invoice.php?order=<?php echo $order_details['order_number']; ?>" 
                   class="btn btn-outline-secondary" target="_blank">
                    <i class="fas fa-download me-2"></i>
                    इनवॉइस डाउनलोड करें
                </a>
            </div>
        </div>
    </div>
</div>

<style>
.success-animation {
    animation: successPulse 2s ease-in-out infinite;
}

@keyframes successPulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
}

.timeline {
    position: relative;
    padding-left: 30px;
}

.timeline::before {
    content: '';
    position: absolute;
    left: 15px;
    top: 0;
    bottom: 0;
    width: 2px;
    background: #e9ecef;
}

.timeline-item {
    position: relative;
    margin-bottom: 30px;
}

.timeline-marker {
    position: absolute;
    left: -22px;
    top: 0;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 3px solid #fff;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.timeline-item.completed .timeline-marker {
    background-color: #28a745;
}

.timeline-item.active .timeline-marker {
    background-color: #007bff;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { box-shadow: 0 0 0 0 rgba(0, 123, 255, 0.7); }
    70% { box-shadow: 0 0 0 10px rgba(0, 123, 255, 0); }
    100% { box-shadow: 0 0 0 0 rgba(0, 123, 255, 0); }
}

.timeline-content h6 {
    margin-bottom: 5px;
    font-weight: 600;
}

.detail-item label {
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

@media (max-width: 768px) {
    .action-buttons .btn {
        display: block;
        width: 100%;
        margin-bottom: 10px;
    }
    
    .timeline {
        padding-left: 20px;
    }
    
    .timeline-marker {
        left: -15px;
        width: 25px;
        height: 25px;
    }
}
</style>

<script>
// Confetti animation on page load
document.addEventListener('DOMContentLoaded', function() {
    // Simple confetti effect
    createConfetti();
});

function createConfetti() {
    const colors = ['#ff6b6b', '#4ecdc4', '#45b7d1', '#96ceb4', '#ffeaa7'];
    const confettiCount = 50;
    
    for (let i = 0; i < confettiCount; i++) {
        setTimeout(() => {
            const confetti = document.createElement('div');
            confetti.style.position = 'fixed';
            confetti.style.left = Math.random() * 100 + 'vw';
            confetti.style.top = '-10px';
            confetti.style.width = '10px';
            confetti.style.height = '10px';
            confetti.style.backgroundColor = colors[Math.floor(Math.random() * colors.length)];
            confetti.style.borderRadius = '50%';
            confetti.style.pointerEvents = 'none';
            confetti.style.zIndex = '9999';
            confetti.style.animation = 'fall 3s linear forwards';
            
            document.body.appendChild(confetti);
            
            setTimeout(() => {
                confetti.remove();
            }, 3000);
        }, i * 100);
    }
}

// Add CSS for confetti animation
const style = document.createElement('style');
style.textContent = `
    @keyframes fall {
        to {
            transform: translateY(100vh) rotate(360deg);
        }
    }
`;
document.head.appendChild(style);

// Auto-redirect to dashboard after 30 seconds
setTimeout(() => {
    if (confirm('क्या आप डैशबोर्ड पर जाना चाहते हैं?')) {
        window.location.href = 'client/dashboard.php';
    }
}, 30000);
</script>

<?php include 'includes/footer.php'; ?>
