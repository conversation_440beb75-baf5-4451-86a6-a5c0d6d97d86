<?php
require_once 'config/database.php';
require_once 'classes/User.php';
require_once 'classes/Order.php';
require_once 'classes/Payment.php';

// Check if user is logged in
if (!User::isLoggedIn()) {
    header('Location: auth/login.php');
    exit();
}

$page_title = 'पेमेंट करें - वेब डेवलपमेंट सेवाएं';
$meta_description = 'सुरक्षित Razorpay पेमेंट गेटवे के माध्यम से अपना भुगतान पूरा करें';

// Get order number from URL
$order_number = $_GET['order'] ?? '';

if (empty($order_number)) {
    $_SESSION['error_message'] = 'ऑर्डर नंबर नहीं मिला।';
    header('Location: client/dashboard.php');
    exit();
}

// Get order details
$order = new Order();
if (!$order->getByOrderNumber($order_number)) {
    $_SESSION['error_message'] = 'ऑर्डर नहीं मिला।';
    header('Location: client/dashboard.php');
    exit();
}

// Check if user owns this order
if ($order->user_id != $_SESSION['user_id']) {
    $_SESSION['error_message'] = 'अनधिकृत पहुंच।';
    header('Location: client/dashboard.php');
    exit();
}

// Check if payment is already completed
if ($order->payment_status === 'paid') {
    $_SESSION['success_message'] = 'यह ऑर्डर पहले से भुगतान किया गया है।';
    header('Location: client/order-details.php?id=' . $order->id);
    exit();
}

// Get service details
try {
    $db = Config::getDB();
    $stmt = $db->prepare("SELECT * FROM services WHERE id = ?");
    $stmt->execute([$order->service_id]);
    $service = $stmt->fetch();
} catch(Exception $e) {
    $_SESSION['error_message'] = 'सेवा की जानकारी नहीं मिली।';
    header('Location: client/dashboard.php');
    exit();
}

// For custom services (price = 0), redirect to contact
if ($service['price'] == 0) {
    $_SESSION['info_message'] = 'कस्टम सेवाओं के लिए कृपया संपर्क करें।';
    header('Location: contact.php?service=custom&order=' . $order_number);
    exit();
}

// Create Razorpay order
$payment = new Payment();
$razorpay_order = $payment->createRazorpayOrder($order->id, $order->total_amount);

if (!$razorpay_order) {
    $_SESSION['error_message'] = 'पेमेंट ऑर्डर बनाने में त्रुटि। कृपया बाद में पुनः प्रयास करें।';
    header('Location: client/dashboard.php');
    exit();
}

include 'includes/header.php';
?>

<div class="container mt-5 pt-5">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <!-- Payment Header -->
            <div class="card shadow-lg border-0 mb-4">
                <div class="card-header bg-success text-white py-4">
                    <h3 class="mb-0">
                        <i class="fas fa-credit-card me-2"></i>
                        सुरक्षित पेमेंट
                    </h3>
                </div>
                <div class="card-body p-4">
                    <!-- Order Summary -->
                    <div class="order-summary mb-4">
                        <h5 class="mb-3">ऑर्डर विवरण:</h5>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="info-item mb-3">
                                    <label class="text-muted">ऑर्डर नंबर:</label>
                                    <div class="fw-bold text-primary"><?php echo htmlspecialchars($order->order_number); ?></div>
                                </div>
                                <div class="info-item mb-3">
                                    <label class="text-muted">सेवा:</label>
                                    <div class="fw-bold"><?php echo htmlspecialchars($service['name_hindi']); ?></div>
                                </div>
                                <div class="info-item mb-3">
                                    <label class="text-muted">ग्राहक:</label>
                                    <div class="fw-bold"><?php echo htmlspecialchars($_SESSION['full_name']); ?></div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="info-item mb-3">
                                    <label class="text-muted">ऑर्डर दिनांक:</label>
                                    <div class="fw-bold"><?php echo date('d/m/Y H:i', strtotime($order->created_at)); ?></div>
                                </div>
                                <div class="info-item mb-3">
                                    <label class="text-muted">स्थिति:</label>
                                    <div>
                                        <span class="badge bg-warning">पेमेंट पेंडिंग</span>
                                    </div>
                                </div>
                                <div class="info-item mb-3">
                                    <label class="text-muted">कुल राशि:</label>
                                    <div class="h4 text-success fw-bold">₹<?php echo number_format($order->total_amount); ?></div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Payment Methods -->
                    <div class="payment-methods mb-4">
                        <h5 class="mb-3">भुगतान विकल्प:</h5>
                        <div class="row g-3">
                            <div class="col-md-3 col-6">
                                <div class="payment-method text-center p-3 border rounded">
                                    <i class="fas fa-credit-card fa-2x text-primary mb-2"></i>
                                    <div class="small">क्रेडिट/डेबिट कार्ड</div>
                                </div>
                            </div>
                            <div class="col-md-3 col-6">
                                <div class="payment-method text-center p-3 border rounded">
                                    <i class="fas fa-university fa-2x text-success mb-2"></i>
                                    <div class="small">नेट बैंकिंग</div>
                                </div>
                            </div>
                            <div class="col-md-3 col-6">
                                <div class="payment-method text-center p-3 border rounded">
                                    <i class="fas fa-mobile-alt fa-2x text-info mb-2"></i>
                                    <div class="small">UPI</div>
                                </div>
                            </div>
                            <div class="col-md-3 col-6">
                                <div class="payment-method text-center p-3 border rounded">
                                    <i class="fas fa-wallet fa-2x text-warning mb-2"></i>
                                    <div class="small">वॉलेट</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Security Features -->
                    <div class="security-features mb-4">
                        <div class="row g-3">
                            <div class="col-md-4 text-center">
                                <i class="fas fa-shield-alt fa-2x text-success mb-2"></i>
                                <div class="small">256-bit SSL एन्क्रिप्शन</div>
                            </div>
                            <div class="col-md-4 text-center">
                                <i class="fas fa-lock fa-2x text-primary mb-2"></i>
                                <div class="small">PCI DSS कॉम्प्लायंट</div>
                            </div>
                            <div class="col-md-4 text-center">
                                <i class="fas fa-check-circle fa-2x text-info mb-2"></i>
                                <div class="small">RBI अप्रूव्ड</div>
                            </div>
                        </div>
                    </div>

                    <!-- Payment Button -->
                    <div class="text-center">
                        <button id="payButton" class="btn btn-success btn-lg px-5 py-3">
                            <i class="fas fa-credit-card me-2"></i>
                            ₹<?php echo number_format($order->total_amount); ?> का भुगतान करें
                        </button>
                        <div class="mt-3">
                            <small class="text-muted">
                                <i class="fas fa-info-circle me-1"></i>
                                भुगतान करने पर आप हमारी <a href="legal/terms-of-service.php" target="_blank">सेवा की शर्तों</a> से सहमत होते हैं
                            </small>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Help Section -->
            <div class="row g-4">
                <div class="col-md-6">
                    <div class="card border-0 shadow-sm">
                        <div class="card-body text-center">
                            <i class="fas fa-headset fa-3x text-primary mb-3"></i>
                            <h5>सहायता चाहिए?</h5>
                            <p class="text-muted">हमारी टीम आपकी मदद के लिए तैयार है</p>
                            <a href="contact.php" class="btn btn-outline-primary">संपर्क करें</a>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card border-0 shadow-sm">
                        <div class="card-body text-center">
                            <i class="fas fa-undo fa-3x text-success mb-3"></i>
                            <h5>रिफंड पॉलिसी</h5>
                            <p class="text-muted">आसान रिफंड प्रक्रिया</p>
                            <a href="legal/refund-policy.php" target="_blank" class="btn btn-outline-success">पढ़ें</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Payment Processing Modal -->
<div class="modal fade" id="paymentModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-body text-center p-5">
                <div class="spinner-border text-primary mb-3" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
                <h5>पेमेंट प्रोसेसिंग...</h5>
                <p class="text-muted">कृपया इस पेज को बंद न करें</p>
            </div>
        </div>
    </div>
</div>

<script>
// Razorpay configuration
const razorpayOptions = {
    "key": "<?php echo Config::RAZORPAY_KEY_ID; ?>",
    "amount": <?php echo $order->total_amount * 100; ?>, // Amount in paise
    "currency": "INR",
    "name": "<?php echo Config::SITE_NAME_HINDI; ?>",
    "description": "<?php echo htmlspecialchars($service['name_hindi']); ?>",
    "order_id": "<?php echo $razorpay_order['id']; ?>",
    "handler": function (response) {
        handlePaymentSuccess(response);
    },
    "prefill": {
        "name": "<?php echo htmlspecialchars($_SESSION['full_name']); ?>",
        "email": "<?php echo htmlspecialchars($_SESSION['email']); ?>",
        "contact": ""
    },
    "notes": {
        "order_number": "<?php echo $order->order_number; ?>",
        "user_id": "<?php echo $_SESSION['user_id']; ?>"
    },
    "theme": {
        "color": "#667eea"
    },
    "modal": {
        "ondismiss": function() {
            console.log('Payment modal closed');
        }
    }
};

// Initialize Razorpay
const rzp = new Razorpay(razorpayOptions);

// Payment button click handler
document.getElementById('payButton').addEventListener('click', function() {
    rzp.open();
});

// Handle payment success
function handlePaymentSuccess(response) {
    // Show processing modal
    const modal = new bootstrap.Modal(document.getElementById('paymentModal'));
    modal.show();
    
    // Send payment details to server
    const formData = new FormData();
    formData.append('order_number', '<?php echo $order->order_number; ?>');
    formData.append('razorpay_payment_id', response.razorpay_payment_id);
    formData.append('razorpay_order_id', response.razorpay_order_id);
    formData.append('razorpay_signature', response.razorpay_signature);
    
    fetch('payment-success.php', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        modal.hide();
        if (data.success) {
            window.location.href = 'payment-success.php?order=' + '<?php echo $order->order_number; ?>';
        } else {
            alert('पेमेंट वेरिफिकेशन में त्रुटि: ' + data.message);
            window.location.reload();
        }
    })
    .catch(error => {
        modal.hide();
        console.error('Error:', error);
        alert('पेमेंट प्रोसेसिंग में त्रुटि। कृपया सहायता के लिए संपर्क करें।');
    });
}

// Handle payment errors
rzp.on('payment.failed', function (response) {
    console.error('Payment failed:', response.error);
    
    // Log payment failure
    const formData = new FormData();
    formData.append('order_number', '<?php echo $order->order_number; ?>');
    formData.append('error_code', response.error.code);
    formData.append('error_description', response.error.description);
    
    fetch('payment-failure.php', {
        method: 'POST',
        body: formData
    });
    
    alert('पेमेंट असफल: ' + response.error.description);
});

// Prevent page refresh during payment
window.addEventListener('beforeunload', function(e) {
    if (rzp.isOpen && rzp.isOpen()) {
        e.preventDefault();
        e.returnValue = 'पेमेंट प्रोसेस चल रहा है। क्या आप वाकई पेज छोड़ना चाहते हैं?';
    }
});

// Auto-refresh order status every 30 seconds
setInterval(function() {
    fetch('check-payment-status.php?order=<?php echo $order->order_number; ?>')
        .then(response => response.json())
        .then(data => {
            if (data.payment_status === 'paid') {
                window.location.href = 'payment-success.php?order=' + '<?php echo $order->order_number; ?>';
            }
        })
        .catch(error => console.log('Status check failed:', error));
}, 30000);
</script>

<style>
.payment-method {
    transition: all 0.3s ease;
    cursor: pointer;
}

.payment-method:hover {
    background-color: #f8f9fa;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.info-item label {
    font-size: 0.875rem;
    font-weight: 500;
}

.security-features {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 10px;
    padding: 20px;
}

@media (max-width: 768px) {
    .payment-method {
        margin-bottom: 10px;
    }
    
    .info-item {
        margin-bottom: 15px !important;
    }
}
</style>

<?php include 'includes/footer.php'; ?>
