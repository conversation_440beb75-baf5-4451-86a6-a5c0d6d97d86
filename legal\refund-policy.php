<?php
require_once '../config/database.php';
require_once '../classes/User.php';

$page_title = 'रिफंड नीति - वेब डेवलपमेंट सर्विसेज';
$meta_description = 'हमारी रिफंड नीति पढ़ें। जानें कि किन परिस्थितियों में आप रिफंड के लिए पात्र हैं और रिफंड प्रक्रिया क्या है।';

include '../includes/header.php';
?>

<div class="container mt-5 pt-5">
    <div class="row justify-content-center">
        <div class="col-lg-10">
            <div class="card shadow-lg border-0">
                <div class="card-header bg-warning text-dark py-4">
                    <h1 class="mb-0">
                        <i class="fas fa-undo me-2"></i>
                        रिफंड नीति
                    </h1>
                    <small>अंतिम अपडेट: <?php echo date('d/m/Y'); ?></small>
                </div>
                <div class="card-body p-5">
                    <div class="legal-content">
                        <section class="mb-5">
                            <h2 class="h4 text-warning mb-3">1. रिफंड नीति अवलोकन</h2>
                            <p>
                                <?php echo Config::SITE_NAME_HINDI; ?> में हम ग्राहक संतुष्टि को प्राथमिकता देते हैं। 
                                यह नीति स्पष्ट करती है कि किन परिस्थितियों में रिफंड उपलब्ध है और रिफंड प्रक्रिया क्या है।
                            </p>
                        </section>

                        <section class="mb-5">
                            <h2 class="h4 text-warning mb-3">2. रिफंड पात्रता</h2>
                            
                            <h5>निम्नलिखित स्थितियों में रिफंड उपलब्ध है:</h5>
                            
                            <div class="row mt-4">
                                <div class="col-md-6">
                                    <div class="card border-success">
                                        <div class="card-header bg-success text-white">
                                            <h6 class="mb-0">100% रिफंड</h6>
                                        </div>
                                        <div class="card-body">
                                            <ul class="mb-0">
                                                <li>प्रोजेक्ट शुरू होने से पहले रद्दीकरण</li>
                                                <li>तकनीकी कारणों से हमारी असमर्थता</li>
                                                <li>डुप्लिकेट पेमेंट की स्थिति में</li>
                                                <li>सेवा में महत्वपूर्ण बदलाव</li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="col-md-6">
                                    <div class="card border-warning">
                                        <div class="card-header bg-warning text-dark">
                                            <h6 class="mb-0">50% रिफंड</h6>
                                        </div>
                                        <div class="card-body">
                                            <ul class="mb-0">
                                                <li>प्रोजेक्ट 50% पूरा होने से पहले रद्दीकरण</li>
                                                <li>पारस्परिक सहमति से प्रोजेक्ट रद्दीकरण</li>
                                                <li>गुणवत्ता मानकों में गंभीर कमी</li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </section>

                        <section class="mb-5">
                            <h2 class="h4 text-warning mb-3">3. रिफंड अपात्रता</h2>
                            <div class="alert alert-danger">
                                <h6><i class="fas fa-times-circle me-2"></i>निम्नलिखित स्थितियों में रिफंड उपलब्ध नहीं है:</h6>
                                <ul class="mb-0">
                                    <li>प्रोजेक्ट 50% से अधिक पूरा होने के बाद</li>
                                    <li>वेबसाइट लाइव होने के बाद</li>
                                    <li>क्लाइंट द्वारा अप्रूवल के बाद</li>
                                    <li>डोमेन/होस्टिंग सेटअप के बाद</li>
                                    <li>कस्टम डेवलपमेंट में स्पेसिफिकेशन चेंज</li>
                                    <li>क्लाइंट द्वारा कंटेंट न देने पर देरी</li>
                                    <li>तीसरे पक्ष की सेवाओं की लागत</li>
                                </ul>
                            </div>
                        </section>

                        <section class="mb-5">
                            <h2 class="h4 text-warning mb-3">4. सेवा-विशिष्ट रिफंड नीति</h2>
                            
                            <div class="accordion" id="serviceRefundAccordion">
                                <div class="accordion-item">
                                    <h2 class="accordion-header">
                                        <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#businessRefund">
                                            बिजनेस प्रीमियम पैकेज (₹15,000)
                                        </button>
                                    </h2>
                                    <div id="businessRefund" class="accordion-collapse collapse show" data-bs-parent="#serviceRefundAccordion">
                                        <div class="accordion-body">
                                            <ul>
                                                <li><strong>डिजाइन चरण:</strong> 100% रिफंड (पहले 3 दिन)</li>
                                                <li><strong>डेवलपमेंट चरण:</strong> 50% रिफंड</li>
                                                <li><strong>टेस्टिंग चरण:</strong> 25% रिफंड</li>
                                                <li><strong>लाइव के बाद:</strong> कोई रिफंड नहीं</li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="accordion-item">
                                    <h2 class="accordion-header">
                                        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#ecommerceRefund">
                                            ई-कॉमर्स पैकेज (₹75,000)
                                        </button>
                                    </h2>
                                    <div id="ecommerceRefund" class="accordion-collapse collapse" data-bs-parent="#serviceRefundAccordion">
                                        <div class="accordion-body">
                                            <ul>
                                                <li><strong>प्लानिंग चरण:</strong> 100% रिफंड (पहले 5 दिन)</li>
                                                <li><strong>डिजाइन चरण:</strong> 75% रिफंड</li>
                                                <li><strong>डेवलपमेंट चरण:</strong> 50% रिफंड</li>
                                                <li><strong>इंटीग्रेशन चरण:</strong> 25% रिफंड</li>
                                                <li><strong>लाइव के बाद:</strong> कोई रिफंड नहीं</li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="accordion-item">
                                    <h2 class="accordion-header">
                                        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#customRefund">
                                            कस्टम डेवलपमेंट
                                        </button>
                                    </h2>
                                    <div id="customRefund" class="accordion-collapse collapse" data-bs-parent="#serviceRefundAccordion">
                                        <div class="accordion-body">
                                            <ul>
                                                <li><strong>रिक्वायरमेंट एनालिसिस:</strong> 100% रिफंड</li>
                                                <li><strong>प्रोटोटाइप चरण:</strong> 75% रिफंड</li>
                                                <li><strong>डेवलपमेंट चरण:</strong> माइलस्टोन के अनुसार</li>
                                                <li><strong>डिप्लॉयमेंट के बाद:</strong> कोई रिफंड नहीं</li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </section>

                        <section class="mb-5">
                            <h2 class="h4 text-warning mb-3">5. रिफंड प्रक्रिया</h2>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <h6>रिफंड अनुरोध करने के लिए:</h6>
                                    <ol>
                                        <li>ईमेल या फोन से संपर्क करें</li>
                                        <li>ऑर्डर नंबर और कारण बताएं</li>
                                        <li>आवश्यक दस्तावेज प्रदान करें</li>
                                        <li>हमारी टीम से पुष्टि की प्रतीक्षा करें</li>
                                    </ol>
                                </div>
                                <div class="col-md-6">
                                    <h6>रिफंड समयसीमा:</h6>
                                    <ul>
                                        <li><strong>अनुरोध समीक्षा:</strong> 2-3 कार्य दिवस</li>
                                        <li><strong>अप्रूवल के बाद:</strong> 5-7 कार्य दिवस</li>
                                        <li><strong>बैंक प्रोसेसिंग:</strong> 3-5 कार्य दिवस</li>
                                        <li><strong>कुल समय:</strong> 10-15 कार्य दिवस</li>
                                    </ul>
                                </div>
                            </div>
                        </section>

                        <section class="mb-5">
                            <h2 class="h4 text-warning mb-3">6. रिफंड विधि</h2>
                            <p>रिफंड निम्नलिखित तरीकों से किया जाएगा:</p>
                            <ul>
                                <li><strong>मूल पेमेंट मेथड:</strong> जिस तरीके से भुगतान किया गया था</li>
                                <li><strong>बैंक ट्रांसफर:</strong> यदि मूल मेथड उपलब्ध नहीं है</li>
                                <li><strong>चेक:</strong> विशेष परिस्थितियों में</li>
                            </ul>
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle me-2"></i>
                                <strong>नोट:</strong> रिफंड में बैंक चार्जेज काटे जा सकते हैं।
                            </div>
                        </section>

                        <section class="mb-5">
                            <h2 class="h4 text-warning mb-3">7. आंशिक रिफंड</h2>
                            <p>निम्नलिखित स्थितियों में आंशिक रिफंड दिया जा सकता है:</p>
                            <ul>
                                <li>प्रोजेक्ट स्कोप में कमी</li>
                                <li>कुछ फीचर्स की डिलीवरी न होना</li>
                                <li>गुणवत्ता मानकों में मामूली कमी</li>
                                <li>समय सीमा में देरी (हमारी गलती से)</li>
                            </ul>
                        </section>

                        <section class="mb-5">
                            <h2 class="h4 text-warning mb-3">8. विवाद समाधान</h2>
                            <p>रिफंड विवाद की स्थिति में:</p>
                            <ol>
                                <li><strong>प्रारंभिक चर्चा:</strong> ईमेल या फोन कॉल</li>
                                <li><strong>औपचारिक समीक्षा:</strong> वरिष्ठ प्रबंधन द्वारा</li>
                                <li><strong>मध्यस्थता:</strong> तटस्थ तीसरे पक्ष द्वारा</li>
                                <li><strong>कानूनी कार्रवाई:</strong> अंतिम विकल्प</li>
                            </ol>
                        </section>

                        <section class="mb-5">
                            <h2 class="h4 text-warning mb-3">9. रिफंड अपवाद</h2>
                            <div class="alert alert-warning">
                                <h6><i class="fas fa-exclamation-triangle me-2"></i>निम्नलिखित लागतें रिफंड में शामिल नहीं हैं:</h6>
                                <ul class="mb-0">
                                    <li>डोमेन रजिस्ट्रेशन फीस</li>
                                    <li>तीसरे पक्ष की लाइसेंस फीस</li>
                                    <li>पेमेंट गेटवे चार्जेज</li>
                                    <li>SMS/ईमेल सेवा लागत</li>
                                    <li>प्रीमियम प्लगइन/थीम लागत</li>
                                </ul>
                            </div>
                        </section>

                        <section class="mb-5">
                            <h2 class="h4 text-warning mb-3">10. रिफंड के बाद</h2>
                            <p>रिफंड के बाद:</p>
                            <ul>
                                <li>सभी प्रोजेक्ट फाइलें हमारे पास रहेंगी</li>
                                <li>डोमेन/होस्टिंग एक्सेस बंद हो जाएगा</li>
                                <li>भविष्य की सहायता उपलब्ध नहीं होगी</li>
                                <li>पूरा किया गया काम (यदि कोई हो) सौंप दिया जाएगा</li>
                            </ul>
                        </section>

                        <section class="mb-5">
                            <h2 class="h4 text-warning mb-3">11. संपर्क जानकारी</h2>
                            <p>रिफंड संबंधी प्रश्नों के लिए संपर्क करें:</p>
                            <div class="row">
                                <div class="col-md-6">
                                    <ul class="list-unstyled">
                                        <li><i class="fas fa-envelope me-2"></i> <strong>ईमेल:</strong> <?php echo Config::CONTACT_EMAIL; ?></li>
                                        <li><i class="fas fa-phone me-2"></i> <strong>फोन:</strong> <?php echo Config::CONTACT_PHONE; ?></li>
                                        <li><i class="fab fa-whatsapp me-2"></i> <strong>WhatsApp:</strong> <?php echo Config::WHATSAPP_NUMBER; ?></li>
                                    </ul>
                                </div>
                                <div class="col-md-6">
                                    <div class="card bg-light">
                                        <div class="card-body">
                                            <h6>रिफंड अनुरोध में शामिल करें:</h6>
                                            <ul class="small mb-0">
                                                <li>ऑर्डर नंबर</li>
                                                <li>रिफंड का कारण</li>
                                                <li>बैंक विवरण</li>
                                                <li>संपर्क जानकारी</li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </section>

                        <div class="alert alert-success">
                            <i class="fas fa-handshake me-2"></i>
                            <strong>हमारा वादा:</strong> हम निष्पक्ष और पारदर्शी रिफंड प्रक्रिया का पालन करते हैं। 
                            आपकी संतुष्टि हमारी प्राथमिकता है।
                        </div>
                    </div>
                </div>
                <div class="card-footer bg-light text-center py-3">
                    <div class="btn-group">
                        <a href="../services.php" class="btn btn-warning">
                            <i class="fas fa-arrow-left me-2"></i>
                            सेवाओं पर वापस जाएं
                        </a>
                        <a href="../contact.php" class="btn btn-outline-warning">
                            <i class="fas fa-question-circle me-2"></i>
                            प्रश्न पूछें
                        </a>
                        <?php if (User::isLoggedIn()): ?>
                            <button onclick="requestRefund()" class="btn btn-outline-danger">
                                <i class="fas fa-undo me-2"></i>
                                रिफंड अनुरोध करें
                            </button>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.legal-content h2 {
    border-bottom: 2px solid #e9ecef;
    padding-bottom: 10px;
}

.legal-content section {
    scroll-margin-top: 100px;
}

.legal-content ul {
    padding-left: 20px;
}

.legal-content li {
    margin-bottom: 8px;
    line-height: 1.6;
}

.accordion-button:not(.collapsed) {
    background-color: #fff3cd;
    color: #856404;
}
</style>

<script>
// Refund request function
function requestRefund() {
    <?php if (User::isLoggedIn()): ?>
        const modal = document.createElement('div');
        modal.innerHTML = `
            <div class="modal fade" id="refundModal" tabindex="-1">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">रिफंड अनुरोध</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <form id="refundForm">
                                <div class="mb-3">
                                    <label class="form-label">ऑर्डर नंबर</label>
                                    <input type="text" class="form-control" name="order_number" required>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">रिफंड का कारण</label>
                                    <textarea class="form-control" name="reason" rows="3" required></textarea>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">अतिरिक्त विवरण</label>
                                    <textarea class="form-control" name="details" rows="2"></textarea>
                                </div>
                            </form>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">रद्द करें</button>
                            <button type="button" class="btn btn-danger" onclick="submitRefundRequest()">अनुरोध भेजें</button>
                        </div>
                    </div>
                </div>
            </div>
        `;
        document.body.appendChild(modal);
        new bootstrap.Modal(document.getElementById('refundModal')).show();
    <?php else: ?>
        alert('रिफंड अनुरोध करने के लिए कृपया पहले लॉगिन करें।');
        window.location.href = '../auth/login.php';
    <?php endif; ?>
}

function submitRefundRequest() {
    const form = document.getElementById('refundForm');
    const formData = new FormData(form);
    
    fetch('../api/refund-request.php', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('रिफंड अनुरोध सफलतापूर्वक भेजा गया! हम 2-3 कार्य दिवस में आपसे संपर्क करेंगे।');
            bootstrap.Modal.getInstance(document.getElementById('refundModal')).hide();
        } else {
            alert('त्रुटि: ' + data.message);
        }
    })
    .catch(error => {
        alert('कुछ गलत हुआ। कृपया बाद में पुनः प्रयास करें।');
    });
}

// Calculate refund amount based on project stage
function calculateRefund() {
    // This would be implemented based on actual project data
    console.log('Refund calculation feature');
}
</script>

<?php include '../includes/footer.php'; ?>
