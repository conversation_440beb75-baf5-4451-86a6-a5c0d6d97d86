<?php
require_once 'config/database.php';

class Email {
    private $conn;
    private $smtp_host;
    private $smtp_port;
    private $smtp_username;
    private $smtp_password;
    private $from_email;
    private $from_name;
    
    public function __construct() {
        $this->conn = Config::getDB();
        $this->smtp_host = Config::SMTP_HOST;
        $this->smtp_port = Config::SMTP_PORT;
        $this->smtp_username = Config::SMTP_USERNAME;
        $this->smtp_password = Config::SMTP_PASSWORD;
        $this->from_email = Config::SMTP_FROM_EMAIL;
        $this->from_name = Config::SMTP_FROM_NAME;
    }
    
    // Send email using PHP mail function (basic implementation)
    public function sendEmail($to_email, $to_name, $subject, $body, $template_id = null, $order_id = null) {
        try {
            // Prepare headers
            $headers = [
                'MIME-Version: 1.0',
                'Content-type: text/html; charset=UTF-8',
                'From: ' . $this->from_name . ' <' . $this->from_email . '>',
                'Reply-To: ' . $this->from_email,
                'X-Mailer: PHP/' . phpversion()
            ];
            
            $headers_string = implode("\r\n", $headers);
            
            // Send email
            $success = mail($to_email, $subject, $body, $headers_string);
            
            // Log email
            $this->logEmail($to_email, $subject, $template_id, $order_id, $success ? 'sent' : 'failed');
            
            return $success;
            
        } catch (Exception $e) {
            error_log('Email sending failed: ' . $e->getMessage());
            $this->logEmail($to_email, $subject, $template_id, $order_id, 'failed', $e->getMessage());
            return false;
        }
    }
    
    // Send order confirmation email
    public function sendOrderConfirmation($order_id) {
        try {
            // Get order details
            $order = new Order();
            $order_details = $order->getById($order_id);
            
            if (!$order_details) {
                throw new Exception('Order not found');
            }
            
            // Get email template
            $template = $this->getTemplate('order_confirmation');
            if (!$template) {
                throw new Exception('Email template not found');
            }
            
            // Replace placeholders
            $subject = $this->replacePlaceholders($template['subject_hindi'], $order_details);
            $body = $this->replacePlaceholders($template['body_hindi'], $order_details);
            
            return $this->sendEmail(
                $order_details['customer_email'],
                $order_details['customer_name'],
                $subject,
                $body,
                $template['id'],
                $order_id
            );
            
        } catch (Exception $e) {
            error_log('Order confirmation email failed: ' . $e->getMessage());
            return false;
        }
    }
    
    // Send status update email
    public function sendStatusUpdate($order_id, $old_status, $new_status) {
        try {
            $order = new Order();
            $order_details = $order->getById($order_id);
            
            if (!$order_details) {
                throw new Exception('Order not found');
            }
            
            $template = $this->getTemplate('status_update');
            if (!$template) {
                throw new Exception('Email template not found');
            }
            
            // Add status information to order details
            $order_details['old_status'] = $this->getStatusLabel($old_status);
            $order_details['new_status'] = $this->getStatusLabel($new_status);
            
            $subject = $this->replacePlaceholders($template['subject_hindi'], $order_details);
            $body = $this->replacePlaceholders($template['body_hindi'], $order_details);
            
            return $this->sendEmail(
                $order_details['customer_email'],
                $order_details['customer_name'],
                $subject,
                $body,
                $template['id'],
                $order_id
            );
            
        } catch (Exception $e) {
            error_log('Status update email failed: ' . $e->getMessage());
            return false;
        }
    }
    
    // Send newsletter
    public function sendNewsletter($subject, $content, $subscriber_emails = []) {
        try {
            // If no specific emails provided, get all active subscribers
            if (empty($subscriber_emails)) {
                $subscriber_emails = $this->getActiveSubscribers();
            }
            
            $sent_count = 0;
            $failed_count = 0;
            
            foreach ($subscriber_emails as $email) {
                $success = $this->sendEmail($email, '', $subject, $content);
                if ($success) {
                    $sent_count++;
                } else {
                    $failed_count++;
                }
                
                // Add small delay to avoid overwhelming the server
                usleep(100000); // 0.1 second delay
            }
            
            return [
                'sent' => $sent_count,
                'failed' => $failed_count,
                'total' => count($subscriber_emails)
            ];
            
        } catch (Exception $e) {
            error_log('Newsletter sending failed: ' . $e->getMessage());
            return false;
        }
    }
    
    // Send contact form reply
    public function sendContactReply($inquiry_id, $reply_message) {
        try {
            // Get inquiry details
            $query = "SELECT * FROM contact_inquiries WHERE id = ?";
            $stmt = $this->conn->prepare($query);
            $stmt->execute([$inquiry_id]);
            $inquiry = $stmt->fetch();
            
            if (!$inquiry) {
                throw new Exception('Contact inquiry not found');
            }
            
            $template = $this->getTemplate('contact_reply');
            if (!$template) {
                throw new Exception('Email template not found');
            }
            
            // Prepare data for placeholders
            $data = [
                'customer_name' => $inquiry['name'],
                'original_message' => $inquiry['message'],
                'reply_message' => $reply_message,
                'inquiry_subject' => $inquiry['subject']
            ];
            
            $subject = $this->replacePlaceholders($template['subject_hindi'], $data);
            $body = $this->replacePlaceholders($template['body_hindi'], $data);
            
            $success = $this->sendEmail(
                $inquiry['email'],
                $inquiry['name'],
                $subject,
                $body,
                $template['id']
            );
            
            if ($success) {
                // Update inquiry status
                $update_query = "UPDATE contact_inquiries SET status = 'replied', admin_reply = ?, replied_at = NOW(), replied_by = ? WHERE id = ?";
                $update_stmt = $this->conn->prepare($update_query);
                $update_stmt->execute([$reply_message, $_SESSION['user_id'] ?? null, $inquiry_id]);
            }
            
            return $success;
            
        } catch (Exception $e) {
            error_log('Contact reply email failed: ' . $e->getMessage());
            return false;
        }
    }
    
    // Get email template
    private function getTemplate($template_type) {
        try {
            $query = "SELECT * FROM email_templates WHERE template_type = ? AND is_active = 1 LIMIT 1";
            $stmt = $this->conn->prepare($query);
            $stmt->execute([$template_type]);
            return $stmt->fetch();
        } catch (Exception $e) {
            error_log('Failed to get email template: ' . $e->getMessage());
            return false;
        }
    }
    
    // Replace placeholders in email content
    private function replacePlaceholders($content, $data) {
        $placeholders = [
            '{{customer_name}}' => $data['customer_name'] ?? $data['name'] ?? '',
            '{{order_number}}' => $data['order_number'] ?? '',
            '{{service_name}}' => $data['service_name_hindi'] ?? $data['service_name'] ?? '',
            '{{amount}}' => isset($data['total_amount']) ? number_format($data['total_amount']) : '',
            '{{old_status}}' => $data['old_status'] ?? '',
            '{{new_status}}' => $data['new_status'] ?? '',
            '{{original_message}}' => $data['original_message'] ?? '',
            '{{reply_message}}' => $data['reply_message'] ?? '',
            '{{inquiry_subject}}' => $data['inquiry_subject'] ?? '',
            '{{site_name}}' => Config::SITE_NAME_HINDI,
            '{{site_url}}' => Config::SITE_URL,
            '{{contact_email}}' => Config::CONTACT_EMAIL,
            '{{contact_phone}}' => Config::CONTACT_PHONE,
            '{{current_date}}' => date('d/m/Y'),
            '{{current_year}}' => date('Y')
        ];
        
        return str_replace(array_keys($placeholders), array_values($placeholders), $content);
    }
    
    // Get status label in Hindi
    private function getStatusLabel($status) {
        $labels = [
            'pending' => 'पेंडिंग',
            'confirmed' => 'पुष्ट',
            'in_progress' => 'प्रगति में',
            'completed' => 'पूर्ण',
            'cancelled' => 'रद्द'
        ];
        
        return $labels[$status] ?? $status;
    }
    
    // Get active newsletter subscribers
    private function getActiveSubscribers() {
        try {
            $query = "SELECT email FROM newsletter_subscribers WHERE status = 'active'";
            $stmt = $this->conn->prepare($query);
            $stmt->execute();
            return $stmt->fetchAll(PDO::FETCH_COLUMN);
        } catch (Exception $e) {
            error_log('Failed to get subscribers: ' . $e->getMessage());
            return [];
        }
    }
    
    // Log email activity
    private function logEmail($recipient_email, $subject, $template_id = null, $order_id = null, $status = 'sent', $error_message = '') {
        try {
            $query = "INSERT INTO email_logs (recipient_email, subject, template_id, order_id, status, error_message) 
                     VALUES (?, ?, ?, ?, ?, ?)";
            $stmt = $this->conn->prepare($query);
            return $stmt->execute([$recipient_email, $subject, $template_id, $order_id, $status, $error_message]);
        } catch (Exception $e) {
            error_log('Email logging failed: ' . $e->getMessage());
            return false;
        }
    }
    
    // Subscribe to newsletter
    public function subscribeNewsletter($email, $name = '') {
        try {
            // Check if already subscribed
            $check_query = "SELECT id, status FROM newsletter_subscribers WHERE email = ?";
            $check_stmt = $this->conn->prepare($check_query);
            $check_stmt->execute([$email]);
            $existing = $check_stmt->fetch();
            
            if ($existing) {
                if ($existing['status'] === 'active') {
                    return ['success' => false, 'message' => 'यह ईमेल पहले से सब्सक्राइब है'];
                } else {
                    // Reactivate subscription
                    $update_query = "UPDATE newsletter_subscribers SET status = 'active', subscribed_at = NOW() WHERE email = ?";
                    $update_stmt = $this->conn->prepare($update_query);
                    $update_stmt->execute([$email]);
                    return ['success' => true, 'message' => 'न्यूज़लेटर सब्सक्रिप्शन पुनः सक्रिय'];
                }
            } else {
                // New subscription
                $insert_query = "INSERT INTO newsletter_subscribers (email, name, status) VALUES (?, ?, 'active')";
                $insert_stmt = $this->conn->prepare($insert_query);
                $insert_stmt->execute([$email, $name]);
                
                // Send welcome email
                $this->sendWelcomeEmail($email, $name);
                
                return ['success' => true, 'message' => 'न्यूज़लेटर सब्सक्रिप्शन सफल'];
            }
            
        } catch (Exception $e) {
            error_log('Newsletter subscription failed: ' . $e->getMessage());
            return ['success' => false, 'message' => 'सब्सक्रिप्शन में त्रुटि'];
        }
    }
    
    // Send welcome email to new subscriber
    private function sendWelcomeEmail($email, $name) {
        $subject = 'न्यूज़लेटर सब्सक्रिप्शन की पुष्टि - ' . Config::SITE_NAME_HINDI;
        $body = "
        <h2>स्वागत है!</h2>
        <p>प्रिय " . htmlspecialchars($name ?: 'ग्राहक') . ",</p>
        <p>आपने हमारे न्यूज़लेटर को सफलतापूर्वक सब्सक्राइब किया है। अब आपको नियमित रूप से वेब डेवलपमेंट की नवीनतम जानकारी और ऑफर्स मिलते रहेंगे।</p>
        <p>धन्यवाद!</p>
        <p><strong>" . Config::SITE_NAME_HINDI . "</strong></p>
        ";
        
        return $this->sendEmail($email, $name, $subject, $body);
    }
    
    // Unsubscribe from newsletter
    public function unsubscribeNewsletter($email) {
        try {
            $query = "UPDATE newsletter_subscribers SET status = 'unsubscribed', unsubscribed_at = NOW() WHERE email = ?";
            $stmt = $this->conn->prepare($query);
            return $stmt->execute([$email]);
        } catch (Exception $e) {
            error_log('Newsletter unsubscribe failed: ' . $e->getMessage());
            return false;
        }
    }
    
    // Get email statistics
    public function getEmailStats() {
        try {
            $stats = [];
            
            // Total emails sent
            $query = "SELECT COUNT(*) as total FROM email_logs WHERE status = 'sent'";
            $stmt = $this->conn->prepare($query);
            $stmt->execute();
            $stats['total_sent'] = $stmt->fetch()['total'];
            
            // Failed emails
            $query = "SELECT COUNT(*) as failed FROM email_logs WHERE status = 'failed'";
            $stmt = $this->conn->prepare($query);
            $stmt->execute();
            $stats['total_failed'] = $stmt->fetch()['failed'];
            
            // Newsletter subscribers
            $query = "SELECT COUNT(*) as subscribers FROM newsletter_subscribers WHERE status = 'active'";
            $stmt = $this->conn->prepare($query);
            $stmt->execute();
            $stats['newsletter_subscribers'] = $stmt->fetch()['subscribers'];
            
            // Emails sent today
            $query = "SELECT COUNT(*) as today FROM email_logs WHERE status = 'sent' AND DATE(sent_at) = CURDATE()";
            $stmt = $this->conn->prepare($query);
            $stmt->execute();
            $stats['sent_today'] = $stmt->fetch()['today'];
            
            return $stats;
            
        } catch (Exception $e) {
            error_log('Failed to get email stats: ' . $e->getMessage());
            return [
                'total_sent' => 0,
                'total_failed' => 0,
                'newsletter_subscribers' => 0,
                'sent_today' => 0
            ];
        }
    }
}
?>
